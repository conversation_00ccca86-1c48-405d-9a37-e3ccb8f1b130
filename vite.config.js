import { defineConfig } from 'vite';
import legacy from '@vitejs/plugin-legacy';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import Unocss from 'unocss/vite';
import path from 'path';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
	base: '',
	resolve: {
		alias: {
			'@': path.resolve(__dirname, 'src'),
		},
	},
	plugins: [
		vue(),
		legacy({
			targets: ['defaults', 'not IE 11'],
		}),
		Unocss(),
		AutoImport({
			resolvers: [ElementPlusResolver()],
			imports: [
				{
					'@/common/js/request': ['request'],
					'@/store': ['useStore'],
					'@/common/js/event': [['default', 'globalEvent']],
				},
			],
		}),
		Components({
			resolvers: [ElementPlusResolver()],
		}),
	],
	server: {
		// port: 3000,
		host: '0.0.0.0', // 允许局域网访问
	},
});
