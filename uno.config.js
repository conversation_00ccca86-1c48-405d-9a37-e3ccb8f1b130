import { defineConfig, presetUno, transformerDirectives } from "unocss"

export default defineConfig({
  presets: [presetUno()],
  transformers: [transformerDirectives()],
  theme: {
    colors: {
      primary: "#2680eb",
      secondary: "#4a94ee",
    },
  },
  shortcuts: [
    {
      flexc: "flex justify-center items-center",
      ycenter: "absolute top-[50%] translate-y-[-50%]",
      xcenter: "absolute left-[50%] translate-x-[-50%]",
      xycenter: "absolute top-[50%] left-[50%] translate-y-[-50%] translate-x-[-50%]",
      fullScreen: "fixed w-full h-full left-0 top-0",
    },
  ],
  variants: [
    (matcher) => {
      if (!matcher.startsWith("before:")) return matcher
      return {
        matcher: matcher.slice(7),
        selector: (s) => `${s}::before`,
      }
    },
  ],
})
