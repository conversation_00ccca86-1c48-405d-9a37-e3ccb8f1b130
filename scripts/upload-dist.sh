#!/bin/bash

# 上传 dist 目录到指定路径并重命名为 web 的脚本
# 使用方法: ./upload-dist.sh [目标路径] [服务器地址] [用户名]
# 例如: ./upload-dist.sh /mnt/d/pointclude/TankCapacity/x64/Release 192.168.2.108 root

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "使用方法:"
    echo "  $0 <目标路径> <服务器地址> <用户名> [端口]"
    echo ""
    echo "参数说明:"
    echo "  目标路径    - 服务器上的目标路径 (例如: /var/www/html)"
    echo "  服务器地址  - 服务器IP或域名 (例如: server.example.com)"
    echo "  用户名      - SSH用户名 (例如: root)"
    echo "  端口        - SSH端口 (可选，默认22)"
    echo ""
    echo "示例:"
    echo "  $0 /var/www/html server.example.com root"
    echo "  $0 /var/www/html server.example.com root 2222"
}

# 检查参数
if [ $# -lt 3 ]; then
    print_error "参数不足"
    show_usage
    exit 1
fi

TARGET_PATH="$1"
SERVER="$2"
USERNAME="$3"
PORT="${4:-22}"

# 检查本地 dist 目录是否存在
if [ ! -d "dist" ]; then
    print_error "本地 dist 目录不存在，请先构建项目"
    exit 1
fi

print_info "开始上传 dist 目录到 ${USERNAME}@${SERVER}:${TARGET_PATH}/web"

# 检查 SSH 连接
print_info "测试 SSH 连接..."
if ! ssh -p "$PORT" -o ConnectTimeout=10 -o BatchMode=yes "${USERNAME}@${SERVER}" "echo 'SSH连接成功'" 2>/dev/null; then
    print_error "无法连接到服务器 ${USERNAME}@${SERVER}:${PORT}"
    print_error "请检查服务器地址、用户名、端口和SSH密钥配置"
    exit 1
fi

print_success "SSH 连接测试成功"

# 在服务器上检查并删除现有的 web 目录
print_info "检查服务器上是否存在 ${TARGET_PATH}/web 目录..."
if ssh -p "$PORT" "${USERNAME}@${SERVER}" "[ -d '${TARGET_PATH}/web' ]" 2>/dev/null; then
    print_warning "发现现有的 web 目录，正在删除..."
    ssh -p "$PORT" "${USERNAME}@${SERVER}" "rm -rf '${TARGET_PATH}/web'" || {
        print_error "删除现有 web 目录失败"
        exit 1
    }
    print_success "现有 web 目录已删除"
else
    print_info "服务器上不存在 web 目录"
fi

# 确保目标路径存在
print_info "确保目标路径存在..."
ssh -p "$PORT" "${USERNAME}@${SERVER}" "mkdir -p '${TARGET_PATH}'" || {
    print_error "创建目标路径失败"
    exit 1
}

# 使用 rsync 上传 dist 目录内容到 web 目录
print_info "开始上传文件..."
rsync -avz --progress --delete -e "ssh -p ${PORT}" dist/ "${USERNAME}@${SERVER}:${TARGET_PATH}/web/" || {
    print_error "文件上传失败"
    exit 1
}

print_success "文件上传完成"

# 验证上传结果
print_info "验证上传结果..."
REMOTE_FILE_COUNT=$(ssh -p "$PORT" "${USERNAME}@${SERVER}" "find '${TARGET_PATH}/web' -type f | wc -l" 2>/dev/null || echo "0")
LOCAL_FILE_COUNT=$(find dist -type f | wc -l)

print_info "本地文件数量: ${LOCAL_FILE_COUNT}"
print_info "远程文件数量: ${REMOTE_FILE_COUNT}"

if [ "$REMOTE_FILE_COUNT" -eq "$LOCAL_FILE_COUNT" ]; then
    print_success "文件数量验证通过"
else
    print_warning "文件数量不匹配，请检查上传结果"
fi

# 设置适当的权限（可选）
print_info "设置文件权限..."
ssh -p "$PORT" "${USERNAME}@${SERVER}" "chmod -R 755 '${TARGET_PATH}/web'" || {
    print_warning "设置文件权限失败，但上传已完成"
}

print_success "部署完成！"
print_info "访问路径: ${TARGET_PATH}/web"
print_info "如果是 Web 服务器，请确保服务器配置正确指向该目录"
