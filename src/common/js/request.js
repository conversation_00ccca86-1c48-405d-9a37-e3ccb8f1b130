import CMD from '@/common/js/cmd';
import mockApi from '@/mock';
import Event from '@/common/js/event';

async function mock(name, data) {
	console.log('前端请求：', name, data);
	const res = await mockApi[name](data);
	const code = res.code.split('_')[0];
	console.log('接口返回：', name, res);
	if (code == 0) {
		return Promise.resolve(res.data);
	}
	return Promise.reject(res);
}

async function fetch(name, data) {
	const fn = CMD[name];
	return new Promise((resolve, reject) => {
		console.log('前端请求：', name, data);
		window.Channel.call(CMD[name], data);
		Event.once(fn, (res) => {
			const code = res.code.split('_')[0];
			console.log(`前端请求返回->`, res);
			if (code == 0) {
				resolve(res.data);
			} else {
				reject(res);
			}
		});
	});
}

export const request = import.meta.env.mock ? mock : fetch;
