/* ===== 现代化客户端应用主题配置 ===== */

:root {
	/* === 色彩系统 === */
	/* 主色调 - 蓝色系 */
	--app-primary: #4a90e2;
	--app-primary-dark: #357abd;
	--app-primary-light: #6ba3e8;
	--app-primary-gradient: linear-gradient(135deg, #4a90e2, #357abd);

	/* 背景色系 - 浅灰色调 */
	--app-bg-primary: #f5f5f5;
	--app-bg-secondary: #ffffff;
	--app-bg-tertiary: #fafafa;
	--app-bg-elevated: #ffffff;
	--app-bg-overlay: rgba(0, 0, 0, 0.3);

	/* 表面色系 */
	--app-surface-1: #ffffff;
	--app-surface-2: #f8f9fa;
	--app-surface-3: #e9ecef;
	--app-surface-4: #dee2e6;

	/* 文字色系 */
	--app-text-primary: #212529;
	--app-text-secondary: #6c757d;
	--app-text-tertiary: #adb5bd;
	--app-text-disabled: #ced4da;

	/* 边框色系 */
	--app-border-primary: #dee2e6;
	--app-border-secondary: #e9ecef;
	--app-border-light: #f0f0f0; /* 轻色边框，用于分隔区域 */
	--app-border-accent: #4a90e2;

	/* 状态色系 */
	--app-success: #52c41a;
	--app-warning: #faad14;
	--app-error: #ff4d4f;
	--app-info: #1890ff;

	/* 阴影系统 */
	--app-shadow-small: 0 1px 3px rgba(0, 0, 0, 0.1);
	--app-shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.12);
	--app-shadow-large: 0 4px 16px rgba(0, 0, 0, 0.15);
	--app-shadow-glow: 0 0 12px rgba(74, 144, 226, 0.2);

	/* 圆角系统 */
	--app-radius-small: 4px;
	--app-radius-medium: 8px;
	--app-radius-large: 12px;
	--app-radius-xl: 16px;

	/* 间距系统 */
	--app-space-xs: 4px;
	--app-space-sm: 8px;
	--app-space-md: 16px;
	--app-space-lg: 24px;
	--app-space-xl: 32px;
	--app-space-2xl: 48px;

	/* 字体系统 */
	--app-font-family: 'Inter', 'Segoe UI', 'PingFang SC', 'Microsoft YaHei',
		sans-serif;
	--app-font-mono: 'JetBrains Mono', 'Consolas', 'Monaco', monospace;

	--app-font-size-xs: 12px;
	--app-font-size-sm: 13px;
	--app-font-size-base: 14px;
	--app-font-size-lg: 16px;
	--app-font-size-xl: 18px;
	--app-font-size-2xl: 20px;
	--app-font-size-3xl: 24px;

	--app-font-weight-normal: 400;
	--app-font-weight-medium: 500;
	--app-font-weight-semibold: 600;
	--app-font-weight-bold: 700;

	/* 层级系统 */
	--app-z-dropdown: 1000;
	--app-z-modal: 2000;
	--app-z-notification: 3000;
	--app-z-tooltip: 4000;
	--app-z-loading: 9999;

	/* 动画系统 */
	--app-duration-fast: 0.1s;
	--app-duration-normal: 0.2s;
	--app-duration-slow: 0.3s;
	--app-duration-slower: 0.5s;

	--app-ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
	--app-ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
	--app-ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);

	/* 毛玻璃效果 */
	--app-glass-bg: rgba(255, 255, 255, 0.05);
	--app-glass-border: rgba(255, 255, 255, 0.1);
	--app-glass-blur: blur(20px);
}

/* ===== 全局重置和基础样式 ===== */
* {
	box-sizing: border-box;
}

html,
body {
	margin: 0;
	padding: 0;
	font-family: var(--app-font-family);
	background: var(--app-bg-primary);
	color: var(--app-text-primary);
	overflow: hidden;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

#app {
	width: 100vw;
	height: 100vh;
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}

::-webkit-scrollbar-track {
	background: var(--app-surface-2);
	border-radius: var(--app-radius-small);
}

::-webkit-scrollbar-thumb {
	background: var(--app-text-tertiary);
	border-radius: var(--app-radius-small);
	transition: background var(--app-duration-normal);
}

::-webkit-scrollbar-thumb:hover {
	background: var(--app-text-secondary);
}

/* ===== 通用动画类 ===== */
.fade-enter-active,
.fade-leave-active {
	transition: opacity var(--app-duration-normal) var(--app-ease-out);
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
	transition: transform var(--app-duration-slow) var(--app-ease-out);
}

.slide-enter-from {
	transform: translateX(-100%);
}

.slide-leave-to {
	transform: translateX(100%);
}

.scale-enter-active,
.scale-leave-active {
	transition: all var(--app-duration-normal) var(--app-ease-out);
}

.scale-enter-from,
.scale-leave-to {
	opacity: 0;
	transform: scale(0.9);
}

/* ===== 毛玻璃卡片组件 ===== */
.glass-card {
	background: var(--app-glass-bg);
	backdrop-filter: var(--app-glass-blur);
	border: 1px solid var(--app-glass-border);
	border-radius: var(--app-radius-large);
	box-shadow: var(--app-shadow-medium);
}

.glass-panel {
	background: var(--app-glass-bg);
	backdrop-filter: var(--app-glass-blur);
	border: 1px solid var(--app-glass-border);
	border-radius: var(--app-radius-medium);
}

/* ===== 现代化按钮样式 ===== */
.modern-button {
	background: var(--app-primary-gradient);
	border: none;
	border-radius: var(--app-radius-medium);
	color: white;
	padding: var(--app-space-sm) var(--app-space-md);
	font-weight: var(--app-font-weight-medium);
	cursor: pointer;
	transition: all var(--app-duration-normal) var(--app-ease-out);
	box-shadow: var(--app-shadow-small);
}

.modern-button:hover {
	transform: translateY(-1px);
	box-shadow: var(--app-shadow-medium);
}

.modern-button:active {
	transform: translateY(0);
}

/* ===== 现代化输入框样式 ===== */
.modern-input {
	background: var(--app-surface-2);
	border: 1px solid var(--app-border-primary);
	border-radius: var(--app-radius-medium);
	color: var(--app-text-primary);
	padding: var(--app-space-sm) var(--app-space-md);
	transition: all var(--app-duration-normal) var(--app-ease-out);
}

.modern-input:focus {
	outline: none;
	border-color: var(--app-primary);
	box-shadow: var(--app-shadow-glow);
}

/* ===== 选择状态样式 ===== */
.selected {
	background: var(--app-primary-gradient) !important;
	color: white !important;
	box-shadow: var(--app-shadow-glow);
}

.highlighted {
	background: var(--app-surface-4) !important;
	border-color: var(--app-primary) !important;
}

/* ===== 工具提示样式 ===== */
.tooltip {
	background: var(--app-bg-elevated);
	color: var(--app-text-primary);
	border: 1px solid var(--app-border-primary);
	border-radius: var(--app-radius-medium);
	box-shadow: var(--app-shadow-medium);
	backdrop-filter: var(--app-glass-blur);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
	:root {
		--app-space-md: 12px;
		--app-space-lg: 20px;
		--app-space-xl: 28px;
		--app-font-size-base: 13px;
	}
}

@media (max-width: 480px) {
	:root {
		--app-space-md: 10px;
		--app-space-lg: 16px;
		--app-space-xl: 24px;
		--app-font-size-base: 12px;
	}
}
