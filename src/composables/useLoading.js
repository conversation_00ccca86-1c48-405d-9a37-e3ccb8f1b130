import { ref, reactive } from 'vue';

// 全局Loading状态
const loadingState = reactive({
	visible: false,
	title: '正在加载资源',
	statusText: '初始化中...',
	steps: [],
	currentStepIndex: 0,
	stepProgress: 0,
});

export function useLoading() {
	// 设置加载步骤
	const setSteps = (steps) => {
		loadingState.steps = steps.map((step, index) => ({
			...step,
			index,
			status: 'pending', // pending, active, completed, error
		}));
		loadingState.currentStepIndex = 0;
	};

	// 显示加载界面
	const show = (title = '正在加载资源') => {
		loadingState.visible = true;
		loadingState.title = title;
	};

	// 隐藏加载界面
	const hide = () => {
		loadingState.visible = false;
		// 延迟重置状态，等待动画完成
		setTimeout(() => {
			if (!loadingState.visible) {
				reset();
			}
		}, 300);
	};

	// 更新状态文本
	const updateStatus = (statusText) => {
		loadingState.statusText = statusText;
	};

	// 开始某个步骤
	const startStep = (stepIndex, status) => {
		if (stepIndex >= 0 && stepIndex < loadingState.steps.length) {
			// 标记之前的步骤为完成
			for (let i = 0; i < stepIndex; i++) {
				loadingState.steps[i].status = 'completed';
			}

			// 标记当前步骤为活动
			loadingState.steps[stepIndex].status = 'active';
			loadingState.currentStepIndex = stepIndex;

			// 标记之后的步骤为待处理
			for (let i = stepIndex + 1; i < loadingState.steps.length; i++) {
				loadingState.steps[i].status = 'pending';
			}

			// 重置步骤进度
			loadingState.stepProgress = 0;

			if (status) {
				updateStatus(status);
			}
		}
	};

	// 完成某个步骤
	const completeStep = (stepIndex, status) => {
		if (stepIndex >= 0 && stepIndex < loadingState.steps.length) {
			loadingState.steps[stepIndex].status = 'completed';
			loadingState.stepProgress = 100;

			if (status) {
				updateStatus(status);
			}
		}
	};

	// 完成当前步骤并移动到下一步
	const nextStep = (status) => {
		if (loadingState.currentStepIndex < loadingState.steps.length) {
			completeStep(loadingState.currentStepIndex);

			if (loadingState.currentStepIndex + 1 < loadingState.steps.length) {
				setTimeout(() => {
					startStep(loadingState.currentStepIndex + 1, status);
				}, 100);
			}
		}
	};

	// 更新当前步骤的进度
	const updateStepProgress = (progress) => {
		loadingState.stepProgress = Math.max(0, Math.min(100, progress));
	};

	// 设置步骤错误状态
	const setStepError = (stepIndex, errorMessage) => {
		if (stepIndex >= 0 && stepIndex < loadingState.steps.length) {
			loadingState.steps[stepIndex].status = 'error';
			updateStatus(errorMessage || '加载失败');
		}
	};

	// 重置所有状态
	const reset = () => {
		loadingState.currentStepIndex = 0;
		loadingState.stepProgress = 0;
		loadingState.statusText = '初始化中...';
		loadingState.steps.forEach((step) => {
			step.status = 'pending';
		});
	};

	return {
		// 状态
		loadingState,

		// 方法
		setSteps,
		show,
		hide,
		updateStatus,
		startStep,
		completeStep,
		nextStep,
		updateStepProgress,
		setStepError,
		reset,
	};
}

// 创建全局实例
export const globalLoading = useLoading();
