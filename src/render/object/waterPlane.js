import * as THREE from 'three';

export class WaterPlane {
	constructor(width, height) {
		this.width = width;
		this.height = height;
		this.water = null;
		this.sky = null;
		this.sun = new THREE.Vector3();
		this.waterNormals2 = null; // 存储第二个法线贴图引用
		this.originalNormalMap = null; // 存储原始法线贴图引用
	}

	createWaterPlane() {
		const waterGeometry = new THREE.PlaneGeometry(10000, 10000);

		// 加载水波纹理作为法线贴图
		const waterNormals = new THREE.TextureLoader().load('/waternormals.jpg');
		waterNormals.wrapS = waterNormals.wrapT = THREE.RepeatWrapping;
		waterNormals.repeat.set(512, 512); // 增加重复次数，让波纹更细腻

		// 创建第二个法线贴图用于叠加效果
		this.waterNormals2 = waterNormals.clone();
		this.waterNormals2.repeat.set(384, 384); // 调整第二层波纹密度

		// 保存原始法线贴图引用
		this.originalNormalMap = waterNormals;

		// 使用物理材质创建更真实的水面效果
		const waterMaterial = new THREE.MeshStandardMaterial({
			color: 0x2a4a6a, // 调整为更自然的蓝色
			normalMap: waterNormals,
			transparent: true,
			opacity: 0.8,
			roughness: 0.9, // 更低的粗糙度，增强反射效果
			metalness: 0, // 非金属
			normalScale: new THREE.Vector2(0.3, 0.3), // 降低法线强度，让波纹更细腻
			side: THREE.DoubleSide,
		});

		// 创建简单的水面网格
		this.water = new THREE.Mesh(waterGeometry, waterMaterial);
		this.water.rotation.x = -Math.PI / 2;
		this.water.position.y = -7.5; // 稍微提高水面位置，使其更明显
		// 设置动画更新所需的时间属性
		this.water.userData.time = 0;

		return this.water;
	}

	// 更新水面动画（需要在渲染循环中调用）
	update(time) {
		if (this.water && this.water.material && this.water.material.normalMap) {
			// 更新主法线贴图偏移 - 调整为更自然的波纹运动
			this.water.material.normalMap.offset.x =
				Math.sin(time * 0.02) * 0.05 + time * 0.008;
			this.water.material.normalMap.offset.y =
				Math.cos(time * 0.015) * 0.05 + time * 0.006;

			// 更新第二个法线贴图，创建叠加波纹效果
			if (this.waterNormals2) {
				this.waterNormals2.offset.x =
					Math.cos(time * 0.025) * 0.03 - time * 0.004;
				this.waterNormals2.offset.y =
					Math.sin(time * 0.018) * 0.03 + time * 0.005;

				// 更温和的法线贴图切换，创建更自然的波纹变化
				const blendFactor = (Math.sin(time * 0.002) + 1) * 0.5;
				if (blendFactor > 0.6) {
					this.water.material.normalMap = this.waterNormals2;
				} else {
					this.water.material.normalMap = this.originalNormalMap;
				}
			}

			// 保存时间值
			this.water.userData.time = time;
		}
	}

	// 设置水面颜色
	setWaterColor(color) {
		if (this.water && this.water.material) {
			this.water.material.color.setHex(color);
		}
	}

	// 设置水面透明度
	setWaterOpacity(opacity) {
		if (this.water && this.water.material) {
			this.water.material.transparent = opacity < 1.0;
			this.water.material.opacity = opacity;
		}
	}

	// 预设舒适的蓝灰色
	setComfortableColor() {
		this.setWaterColor(0x4a6b8a); // 柔和的蓝灰色
		this.setWaterOpacity(0.8);
	}

	// 预设深蓝色
	setDeepBlue() {
		this.setWaterColor(0x2c5f7c); // 深蓝色
		this.setWaterOpacity(0.85);
	}

	// 预设浅蓝色
	setLightBlue() {
		this.setWaterColor(0x6b9dc2); // 浅蓝色
		this.setWaterOpacity(0.75);
	}
}
