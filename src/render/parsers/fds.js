import { v4 } from 'uuid';

/**
 * FDS到JSON格式转换解析器
 * 专门处理FDS文件转换为结构化JSON数据
 */
export class FDSParser {
	constructor() {
		// 初始化颜色名称映射表
		this.colorNameMap = {
			RED: '#ff0000',
			WHITE: '#ffffff',
			YELLOW: '#ffff00',
			BLUE: '#0000ff',
			GREEN: '#00ff00',
			BLACK: '#000000',
			GRAY: '#808080',
			'GRAY 80': '#cccccc',
			ORANGE: '#ffa500',
			PURPLE: '#800080',
			PINK: '#ffc0cb',
			BROWN: '#a52a2a',
		};
	}

	/**
	 * 解析FDS文件内容为结构化JSON
	 * @param {string} content - FDS文件内容
	 * @returns {object} 结构化的JSON数据
	 */
	parseFDSToJSON(content) {
		if (!content) {
			console.error('FDS文件内容为空');
			return null;
		}

		// 首先预处理内容，合并多行命令
		const preprocessedContent = this.preprocessMultilineCommands(content);
		const lines = preprocessedContent.split('\n');

		// 初始化结果结构
		const result = {
			metadata: {
				filename: '',
				generator: '',
				date: '',
			},
			head: null,
			time: null,
			dump: null,
			mesh: [],
			spec: [],
			reac: [],
			surf: [],
			obst: [],
			hole: [],
			other: [],
			surfaceColorMap: {},
			statistics: {},
			bounds: {},
		};

		// 逐行解析
		lines.forEach((line, index) => {
			const trimmedLine = line.trim();

			// 跳过空行和注释行，但提取元数据
			if (!trimmedLine || trimmedLine.startsWith('!')) {
				// 检查是否是元数据注释
				if (trimmedLine.includes('Generated by')) {
					result.metadata.generator = trimmedLine;
				} else if (trimmedLine.match(/\d{4}年\d{1,2}月\d{1,2}日/)) {
					result.metadata.date = trimmedLine;
				} else if (index === 0 && trimmedLine.endsWith('.fds')) {
					result.metadata.filename = trimmedLine;
				}
				return;
			}

			// 解析不同类型的数据块
			if (trimmedLine.startsWith('&HEAD')) {
				result.head = this.parseNamelistLine(trimmedLine);
			} else if (trimmedLine.startsWith('&TIME')) {
				result.time = this.parseNamelistLine(trimmedLine);
			} else if (trimmedLine.startsWith('&DUMP')) {
				result.dump = this.parseNamelistLine(trimmedLine);
			} else if (trimmedLine.startsWith('&MESH')) {
				result.mesh.push(this.parseNamelistLine(trimmedLine));
			} else if (trimmedLine.startsWith('&SPEC')) {
				result.spec.push(this.parseNamelistLine(trimmedLine));
			} else if (trimmedLine.startsWith('&REAC')) {
				result.reac.push(this.parseNamelistLine(trimmedLine));
			} else if (trimmedLine.startsWith('&SURF')) {
				result.surf.push(this.parseNamelistLine(trimmedLine));
			} else if (trimmedLine.startsWith('&OBST')) {
				result.obst.push(this.parseNamelistLine(trimmedLine));
			} else if (trimmedLine.startsWith('&HOLE')) {
				result.hole.push(this.parseNamelistLine(trimmedLine));
			} else {
				// 其他类型的数据块
				result.other.push({
					type: 'unknown',
					line: trimmedLine,
					lineNumber: index + 1,
				});
			}
		});

		// 生成surfaceColorMap
		result.surfaceColorMap = this.generateSurfaceColorMap(result.surf);

		// 生成统计信息
		result.statistics = {
			totalLines: lines.length,
			obstCount: result.obst.length,
			holeCount: result.hole.length,
			surfCount: result.surf.length,
			meshCount: result.mesh.length,
			specCount: result.spec.length,
			reacCount: result.reac.length,
		};

		// 计算边界
		result.bounds = this.calculateBounds(result);

		return result;
	}

	/**
	 * 预处理多行命令，将跨行的FDS命令合并为单行
	 * @param {string} content - 原始FDS内容
	 * @returns {string} 预处理后的内容
	 */
	preprocessMultilineCommands(content) {
		const lines = content.split('\n');
		const processedLines = [];
		let currentCommand = '';
		let inCommand = false;

		lines.forEach((line) => {
			const trimmedLine = line.trim();

			// 跳过空行和注释
			if (!trimmedLine || trimmedLine.startsWith('!')) {
				if (currentCommand) {
					// 如果正在处理命令中，保存当前命令
					processedLines.push(currentCommand);
					currentCommand = '';
					inCommand = false;
				}
				processedLines.push(line);
				return;
			}

			// 检查是否是新命令的开始
			if (trimmedLine.startsWith('&')) {
				// 如果有未完成的命令，先保存
				if (currentCommand) {
					processedLines.push(currentCommand);
				}
				currentCommand = trimmedLine;
				inCommand = true;

				// 检查是否在同一行结束
				if (trimmedLine.endsWith('/')) {
					processedLines.push(currentCommand);
					currentCommand = '';
					inCommand = false;
				}
			} else if (inCommand) {
				// 继续当前命令
				currentCommand += ' ' + trimmedLine;

				// 检查是否结束
				if (trimmedLine.endsWith('/')) {
					processedLines.push(currentCommand);
					currentCommand = '';
					inCommand = false;
				}
			} else {
				// 不在命令中，直接添加
				processedLines.push(line);
			}
		});

		// 处理最后一个命令（如果有）
		if (currentCommand) {
			processedLines.push(currentCommand);
		}

		return processedLines.join('\n');
	}

	/**
	 * 解析单行namelist数据
	 * @param {string} line - FDS命令行
	 * @returns {object} 解析后的对象
	 */
	parseNamelistLine(line) {
		// 提取命令类型
		const typeMatch = line.match(/&(\w+)/);
		if (!typeMatch) return null;

		const type = typeMatch[1];

		// 提取参数部分（去掉开头的&TYPE和结尾的/）
		const paramStart = line.indexOf(type) + type.length;
		const paramEnd = line.lastIndexOf('/');

		if (paramEnd === -1) return { type };

		const paramString = line.substring(paramStart, paramEnd).trim();

		// 解析参数
		const params = this.parseParameters(paramString);
		return {
			type,
			...params,
			_id: v4(),
		};
	}

	/**
	 * 解析参数字符串
	 * @param {string} paramString - 参数字符串
	 * @returns {object} 解析后的参数对象
	 */
	parseParameters(paramString) {
		const params = {};

		// 改进的解析逻辑：先拆分参数，然后解析每个键值对
		const paramPairs = [];
		let currentParam = '';
		let insideQuotes = false;
		let depth = 0;

		// 首先拆分参数对
		for (let i = 0; i < paramString.length; i++) {
			const char = paramString[i];

			if (char === "'" && paramString[i - 1] !== '\\') {
				insideQuotes = !insideQuotes;
			}

			if (!insideQuotes) {
				if (char === '(' || char === '[') depth++;
				if (char === ')' || char === ']') depth--;

				if (char === ',' && depth === 0) {
					// 检查是否是参数分隔符（不是值内部的逗号）
					const nextEqual = paramString.indexOf('=', i);
					const nextComma = paramString.indexOf(',', i + 1);

					if (nextEqual !== -1 && (nextComma === -1 || nextEqual < nextComma)) {
						// 这是参数分隔符
						paramPairs.push(currentParam.trim());
						currentParam = '';
						continue;
					}
				}
			}

			currentParam += char;
		}

		if (currentParam.trim()) {
			paramPairs.push(currentParam.trim());
		}

		// 解析每个参数对
		paramPairs.forEach((pair) => {
			const equalIndex = pair.indexOf('=');
			if (equalIndex === -1) return;

			const key = pair.substring(0, equalIndex).trim();
			let value = pair.substring(equalIndex + 1).trim();

			// 处理不同类型的值
			if (value.startsWith("'") && value.endsWith("'")) {
				// 字符串值
				params[key] = value.slice(1, -1);
			} else if (value === '.TRUE.' || value === '.FALSE.') {
				// 布尔值
				params[key] = value === '.TRUE.';
			} else if (key === 'XB' || key === 'IJK' || key === 'RGB') {
				// 特殊字段保持字符串格式
				params[key] = value;
			} else if (value.includes(',')) {
				// 其他数组值
				const arrayValues = value.split(',').map((v) => {
					const trimmed = v.trim();
					if (!isNaN(trimmed) && trimmed !== '') {
						return Number(trimmed);
					}
					return trimmed;
				});
				params[key] = arrayValues;
			} else if (!isNaN(value) && value !== '') {
				// 数值
				params[key] = Number(value);
			} else {
				// 其他类型保持字符串
				params[key] = value;
			}
		});

		return params;
	}

	/**
	 * 生成表面颜色映射表
	 * @param {Array} surfArray - SURF对象数组
	 * @returns {object} 颜色映射表
	 */
	generateSurfaceColorMap(surfArray) {
		const colorMap = {};

		surfArray.forEach((surf) => {
			if (!surf.ID) return;

			let color = '#ffffff'; // 默认白色

			// 优先使用RGB
			if (surf.RGB && typeof surf.RGB === 'string') {
				const rgbValues = surf.RGB.split(',').map((v) => parseInt(v.trim()));
				if (rgbValues.length >= 3) {
					const [r, g, b] = rgbValues;
					color = `#${r.toString(16).padStart(2, '0')}${g
						.toString(16)
						.padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
				}
			}
			// 其次使用COLOR
			else if (surf.COLOR) {
				color = this.colorNameToHex(surf.COLOR);
			}

			colorMap[surf.ID] = color;
		});

		return colorMap;
	}

	/**
	 * 颜色名称转换为十六进制
	 * @param {string} colorName - 颜色名称
	 * @returns {string} 十六进制颜色值
	 */
	colorNameToHex(colorName) {
		return this.colorNameMap[colorName.toUpperCase()] || '#ffffff';
	}

	/**
	 * 获取OBST对象的颜色
	 * @param {object} obst - OBST对象
	 * @param {object} surfaceColorMap - 表面颜色映射表
	 * @returns {string} 十六进制颜色值
	 */
	getObstColor(obst, surfaceColorMap) {
		// if (obst.RGB === '255,249,240') {
		// 	obst.RGB = '255, 255, 51';
		// }
		// if (obst.RGB === '209,199,188') {
		// 	obst.RGB = '255, 255, 51';
		// }

		// 优先级1: 直接RGB属性
		if (obst.RGB && typeof obst.RGB === 'string') {
			const rgbValues = obst.RGB.split(',').map((v) => parseInt(v.trim()));
			if (rgbValues.length >= 3) {
				const [r, g, b] = rgbValues;
				return `#${r.toString(16).padStart(2, '0')}${g
					.toString(16)
					.padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
			}
		}

		// 优先级2: 直接COLOR属性
		if (obst.COLOR) {
			return this.colorNameToHex(obst.COLOR);
		}

		// 优先级3: SURF_ID引用
		if (obst.SURF_ID && surfaceColorMap[obst.SURF_ID]) {
			return surfaceColorMap[obst.SURF_ID];
		}

		// 优先级4: 默认颜色
		return '#ffffff';
	}

	/**
	 * 计算场景边界
	 * @param {object} jsonData - 解析后的JSON数据
	 * @returns {object} 边界信息
	 */
	calculateBounds(jsonData) {
		let minX = Infinity,
			maxX = -Infinity;
		let minY = Infinity,
			maxY = -Infinity;
		let minZ = Infinity,
			maxZ = -Infinity;

		// 从OBST和HOLE中计算边界
		const allObjects = [...jsonData.obst, ...jsonData.hole];

		allObjects.forEach((obj) => {
			if (obj.XB && typeof obj.XB === 'string') {
				// XB现在是字符串格式，需要解析
				const coords = obj.XB.split(',').map(Number);
				if (coords.length === 6) {
					const [x1, x2, y1, y2, z1, z2] = coords;
					minX = Math.min(minX, x1, x2);
					maxX = Math.max(maxX, x1, x2);
					minY = Math.min(minY, y1, y2);
					maxY = Math.max(maxY, y1, y2);
					minZ = Math.min(minZ, z1, z2);
					maxZ = Math.max(maxZ, z1, z2);
				}
			}
		});

		if (minX === Infinity) {
			return {
				minX: 0,
				maxX: 0,
				minY: 0,
				maxY: 0,
				minZ: 0,
				maxZ: 0,
				centerX: 0,
				centerY: 0,
				centerZ: 0,
				sizeX: 0,
				sizeY: 0,
				sizeZ: 0,
			};
		}

		return {
			minX,
			maxX,
			minY,
			maxY,
			minZ,
			maxZ,
			centerX: (minX + maxX) / 2,
			centerY: (minY + maxY) / 2,
			centerZ: (minZ + maxZ) / 2,
			sizeX: maxX - minX,
			sizeY: maxY - minY,
			sizeZ: maxZ - minZ,
		};
	}

	/**
	 * 获取边界信息的便捷方法
	 * @param {object} jsonData - 解析后的JSON数据
	 * @returns {object|null} 边界信息
	 */
	getBounds(jsonData) {
		if (!jsonData || !jsonData.obst) return null;
		return this.calculateBounds(jsonData);
	}

	/**
	 * 处理FDS数据并添加分组信息
	 * @param {object} jsonData - 解析后的JSON数据
	 * @returns {object} 添加了分组信息的JSON数据
	 */
	processWithGroups(jsonData) {
		if (!jsonData || !jsonData.obst) return jsonData;

		// 按ID前缀分组
		const groups = {};

		// 遍历所有obst对象
		jsonData.obst.forEach((item, index) => {
			if (!item.ID) {
				console.warn('FDS项缺少ID:', item);
				return;
			}

			// 提取ID的前缀（不含数字后缀）
			const prefix = this.extractPrefix(item.ID);

			// 如果分组不存在，创建新分组
			if (!groups[prefix]) {
				groups[prefix] = {
					id: `fds_group_${prefix}`,
					label: prefix,
					type: 'fds_group',
					items: [],
				};
			}

			// 将当前项添加到对应分组
			groups[prefix].items.push(item);

			// 在原始obst上添加分组信息
			item.groupId = `fds_group_${prefix}`;
			item.groupLabel = prefix;
		});

		// 将分组信息添加到结果中
		jsonData.groups = Object.values(groups);

		// 添加一些分组统计信息
		jsonData.groupStats = {
			totalGroups: jsonData.groups.length,
			groupCounts: jsonData.groups.map((g) => ({
				label: g.label,
				count: g.items.length,
			})),
		};

		return jsonData;
	}

	/**
	 * 提取ID前缀，用于分组
	 * @param {string} id - 对象ID
	 * @returns {string} 提取的前缀名称
	 */
	extractPrefix(id) {
		// 如果ID为空，返回"其他"
		if (!id) return '其他';

		// 去除ID末尾的数字
		const baseId = id.replace(/\d+$/, '');

		// 如果去除数字后为空，直接返回原始ID
		if (!baseId.trim()) return id;

		// 返回去除末尾数字的前缀作为分组名
		return baseId;
	}
}
