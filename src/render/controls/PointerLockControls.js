import { Euler, EventDispatcher, Vector3 } from 'three';

const _euler = new Euler(0, 0, 0, 'YXZ');
const _vector = new Vector3();

const _changeEvent = { type: 'change' };

const _PI_2 = Math.PI / 2;

class PointerLockControls extends EventDispatcher {
	constructor(camera, domElement) {
		super();

		this.camera = camera;
		this.domElement = domElement;

		this.isMouseDown = false;

		// Set to constrain the pitch of the camera
		// Range is 0 to Math.PI radians
		this.minPolarAngle = 0; // radians
		this.maxPolarAngle = Math.PI; // radians

		this.pointerSpeed = 1.0;

		this._onMouseMove = onMouseMove.bind(this);
		this._onMouseDown = onMouseDown.bind(this);
		this._onMouseUp = onMouseUp.bind(this);
		this._onContextMenu = onContextMenu.bind(this);
		// this.domElement.style.cursor = 'none';

		this.connect();
	}

	connect() {
		this.domElement.addEventListener('mousemove', this._onMouseMove);
		this.domElement.addEventListener('mousedown', this._onMouseDown);
		this.domElement.addEventListener('mouseup', this._onMouseUp);
		this.domElement.addEventListener('contextmenu', this._onContextMenu);
	}

	disconnect() {
		this.domElement.removeEventListener('mousemove', this._onMouseMove);
		this.domElement.removeEventListener('mousedown', this._onMouseDown);
		this.domElement.removeEventListener('mouseup', this._onMouseUp);
		this.domElement.removeEventListener('contextmenu', this._onContextMenu);
	}

	dispose() {
		this.disconnect();
	}

	getObject() {
		// retaining this method for backward compatibility
		return this.camera;
	}

	getDirection(v) {
		return v.set(0, 0, -1).applyQuaternion(this.camera.quaternion);
	}

	moveForward(distance) {
		// move forward parallel to the xz-plane
		// assumes camera.up is y-up

		const camera = this.camera;

		_vector.setFromMatrixColumn(camera.matrix, 0);

		_vector.crossVectors(camera.up, _vector);

		camera.position.addScaledVector(_vector, distance);
	}

	moveRight(distance) {
		const camera = this.camera;

		_vector.setFromMatrixColumn(camera.matrix, 0);

		camera.position.addScaledVector(_vector, distance);
	}
	lock() {
		this.locked = true;
	}
	unlock() {
		this.locked = false;
	}
}

// event listeners

function onContextMenu(event) {
	event.preventDefault();
}

function onMouseDown(event) {
	if (event.button === 0) {
		// 左键
		this.isMouseDown = true;
	}
}

function onMouseUp(event) {
	if (event.button === 0) {
		// 左键
		this.isMouseDown = false;
	}
}

function onMouseMove(event) {
	if (!this.isMouseDown || this.locked) return;

	const movementX =
		event.movementX || event.mozMovementX || event.webkitMovementX || 0;
	const movementY =
		event.movementY || event.mozMovementY || event.webkitMovementY || 0;

	const camera = this.camera;
	_euler.setFromQuaternion(camera.quaternion);

	_euler.y -= movementX * 0.002 * this.pointerSpeed;
	_euler.x -= movementY * 0.002 * this.pointerSpeed;

	_euler.x = Math.max(
		_PI_2 - this.maxPolarAngle,
		Math.min(_PI_2 - this.minPolarAngle, _euler.x)
	);

	camera.quaternion.setFromEuler(_euler);

	this.dispatchEvent(_changeEvent);
}

export { PointerLockControls };
