import * as THREE from 'three';
import { PointerLockControls } from './controls/PointerLockControls.js';
import { FDSParser } from './parsers/fds.js';
import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import Stats from 'three/examples/jsm/libs/stats.module.js';
import { CharacterManager } from './characters';
import { globalLoading } from '../composables/useLoading.js';
import { WaterPlane } from './object/waterPlane.js';

/**
 * 默认材质配置
 */
const DEFAULT_MATERIAL_CONFIG = {
	fds: {
		roughness: 0.4, // 适中的粗糙度
		metalness: 0.3, // 增加金属感，提供更好的反射对比
		envMapIntensity: 0.6, // 适中的环境反射
		transparent: true,
	},
};

/**
 * 默认控制键配置
 */
const DEFAULT_KEYS = {
	w: false,
	s: false,
	a: false,
	d: false,
	q: false,
	e: false,
};

/**
 * 视图引擎类
 * 负责3D场景的渲染、交互、动画等核心功能
 */
export class ViewEngine {
	constructor() {
		// ==================== 核心组件 ====================
		this.clock = new THREE.Clock();
		this.characterManager = new CharacterManager(this);

		// ==================== 材质管理 ====================
		this.fdsMaterials = []; // 统一管理所有FDS材质
		this.currentTransparency = 0; // 默认50%透明度
		this.materialConfig = { ...DEFAULT_MATERIAL_CONFIG };

		// ==================== 控制相关 ====================
		this.speed = 0.03;
		this.keys = { ...DEFAULT_KEYS };
		this.isDragging = false;
		this.dragTimeout = null;

		// ==================== 对象管理 ====================
		this.objectIndexMap = new Map(); // _id -> mesh 的快速映射
		this.groupObjectsMap = new Map(); // groupId -> Set<mesh> 的分组映射
		this.allFdsObjects = new Set(); // 所有FDS对象的集合
		this.hiddenObjects = new Set(); // 隐藏对象的集合
		this.fdsObjects = []; // 存储所有的FDS对象Mesh
		this.clickableObjects = []; // 存储可点击的对象

		// ==================== 交互和高亮 ====================
		this.raycaster = new THREE.Raycaster();
		this.mouse = new THREE.Vector2();
		this.highlightedObject = null;
		this.highlightWireframe = null;
		this.originalMaterial = null; // 存储原始材质
		this.originalCharacterMaterials = new Map(); // 存储人物原始材质
		this.onObjectClick = null; // 对象点击回调函数

		// 注意：init() 现在是异步的，需要从外部调用
	}

	// ==================== 初始化方法 ====================

	/**
	 * 初始化视图引擎
	 * @param {HTMLElement} container - 容器元素
	 */
	async init(container) {
		this.container = container;

		try {
			this._setupLoadingSteps();
			await this._initializeCore();
			await this._loadAssets();
			this._finalizeInitialization();

			// 开始渲染循环
			this.tick();

			// 标记完成并隐藏加载界面
			globalLoading.completeStep(7, '初始化完成');
			setTimeout(() => globalLoading.hide(), 1000);
		} catch (error) {
			this._handleInitializationError(error);
		}
	}

	/**
	 * 设置加载步骤
	 */
	_setupLoadingSteps() {
		globalLoading.show();
		globalLoading.setSteps([
			{ name: '初始化场景', weight: 1 },
			{ name: '加载天空盒', weight: 2 },
			{ name: '设置灯光', weight: 1 },
			{ name: '初始化渲染器', weight: 1 },
			{ name: '加载FDS文件', weight: 3 },
			{ name: '加载角色模型', weight: 4 },
			{ name: '加载移动数据', weight: 3 },
			{ name: '初始化界面', weight: 1 },
		]);
	}

	/**
	 * 初始化核心组件
	 */
	async _initializeCore() {
		// 第一步：初始化场景
		globalLoading.startStep(0, '正在初始化3D场景...');
		this.scene = new THREE.Scene();
		globalLoading.updateStepProgress(100);
		globalLoading.nextStep();

		// 第二步：初始化摄像机和天空盒
		globalLoading.startStep(1, '正在加载天空盒...');
		this.initCamera();
		globalLoading.updateStepProgress(50);
		await this.initSkybox();
		globalLoading.updateStepProgress(100);
		globalLoading.nextStep();

		// 第三步：初始化灯光
		globalLoading.startStep(2, '正在设置场景灯光...');
		this.initLight();
		globalLoading.updateStepProgress(100);
		globalLoading.nextStep();

		// 第四步：初始化渲染器和控制器
		globalLoading.startStep(3, '正在初始化渲染器...');
		this.initRenderer(this.container);
		this.initControls();
		globalLoading.updateStepProgress(100);
		globalLoading.nextStep();
	}

	/**
	 * 加载资源
	 */
	async _loadAssets() {
		// 第五步：加载FDS
		globalLoading.startStep(4, '正在解析FDS文件...');
		await this.loadFds();

		// 第六步：加载角色模型
		globalLoading.startStep(5, '正在加载角色模型...');
		await this._loadCharacters();
	}

	/**
	 * 加载角色相关资源
	 */
	async _loadCharacters() {
		await this.characterManager.loadPersonInfo();
		globalLoading.updateStepProgress(20);

		await this.characterManager.createCharacters();
		// 进度更新已在createCharacters方法内部处理

		await this.characterManager.loadMoveData();
		globalLoading.updateStepProgress(100);
		globalLoading.nextStep();
	}

	/**
	 * 完成初始化
	 */
	_finalizeInitialization() {
		globalLoading.startStep(7, '正在初始化用户界面...');
		this.initWaterPlane();
		// this.initStats();
		this.addResizeListener();
		globalLoading.updateStepProgress(100);
	}

	/**
	 * 处理初始化错误
	 * @param {Error} error - 错误对象
	 */
	_handleInitializationError(error) {
		console.error('初始化失败:', error);
		globalLoading.setStepError(
			globalLoading.loadingState.currentStepIndex,
			'加载失败，请刷新页面重试'
		);
		setTimeout(() => globalLoading.hide(), 3000);
	}

	async loadFds() {
		try {
			let text = '';
			if (import.meta.env.DEV) {
				const response = await fetch('./data/1.fds');
				text = await response.text();
			} else {
				text = await window.Channel.loadFBS((progress) => {
					globalLoading.updateStepProgress(progress);
				});
			}
			this.createFSDMesh(text);
		} catch (error) {
			throw error;
		}
	}
	createFSDMesh(text) {
		const parser = new FDSParser();
		const json = parser.parseFDSToJSON(text);
		// 处理FDS数据并添加分组信息
		const processedJson = parser.processWithGroups(json);

		const bounds = parser.getBounds(processedJson);
		this.bounds = bounds;
		this.fdsJson = processedJson; // 保存处理后的FDS解析数据

		// 确保instanceDataMap已初始化
		if (!this.instanceDataMap) {
			this.instanceDataMap = new Map();
		}

		// 按分组创建网格
		if (processedJson.groups && processedJson.groups.length > 0) {
			processedJson.groups.forEach((group) => {
				// 将组内物体按透明度分类
				const normalObsts = [];
				const transparentObsts = [];

				group.items.forEach((obst) => {
					if (['墙', '窗'].some((item) => obst.ID.includes(item))) {
						transparentObsts.push(obst);
					} else {
						normalObsts.push(obst);
					}
				});

				// 为普通材质的物体创建网格
				if (normalObsts.length > 0) {
					this.createMeshes(
						normalObsts,
						bounds,
						parser,
						processedJson.surf,
						false,
						group
					);
				}

				// 为透明材质的物体创建网格
				if (transparentObsts.length > 0) {
					this.createMeshes(
						transparentObsts,
						bounds,
						parser,
						processedJson.surf,
						true,
						group
					);
				}
			});
		} else {
			// 如果没有分组信息，使用原来的方法
			// 确保每个obst都有唯一的_id
			if (processedJson.obst && processedJson.obst.length > 0) {
				processedJson.obst.forEach((obst, index) => {
					if (!obst._id) {
						obst._id = `obst_${Date.now()}_${index}`;
					}
				});

				// 统一创建所有FDS网格，不再区分透明和不透明
				this.createMeshes(
					processedJson.obst,
					bounds,
					parser,
					processedJson.surf
				);
			}
		}

		this.scene.scale.set(0.1, 0.1, 0.1);
	}

	// ==================== 材质管理方法 ====================

	/**
	 * 创建FDS材质
	 * @param {Object} obstData - 障碍物数据
	 * @returns {THREE.MeshStandardMaterial} 创建的材质
	 */
	createFdsMaterial(obstData = null) {
		const config = this.materialConfig.fds;
		const materialVariation = this._getMaterialVariation(obstData);
		const isWallOrWindow = this._isWallOrWindow(obstData);
		const materialOpacity = isWallOrWindow ? this.currentTransparency : 1.0;

		const material = new THREE.MeshStandardMaterial({
			polygonOffset: true,
			polygonOffsetFactor: Math.random() * 2,
			// roughness: config.roughness + materialVariation.roughnessOffset,
			// metalness: config.metalness + materialVariation.metalnessOffset,
			roughness: 0.6,
			metalness: 0.1,
			// envMapIntensity: config.envMapIntensity + materialVariation.envMapOffset,
			transparent: isWallOrWindow ? true : config.transparent,
			opacity: materialOpacity,
		});

		material.userData = { name: obstData?.ID || '' };
		this.fdsMaterials.push(material);
		return material;
	}

	/**
	 * 检查是否为墙或窗材质
	 * @param {Object} obstData - 障碍物数据
	 * @returns {boolean} 是否为墙或窗
	 */
	_isWallOrWindow(obstData) {
		return obstData && ['墙', '窗'].some((item) => obstData.ID.includes(item));
	}

	/**
	 * 获取材质变化参数
	 * @param {Object} obstData - 障碍物数据
	 * @returns {Object} 材质变化参数
	 */
	_getMaterialVariation(obstData) {
		if (!obstData) {
			return { roughnessOffset: 0, metalnessOffset: 0, envMapOffset: 0 };
		}

		// 基于对象的位置或其他属性创建变化
		const hash = this.hashObstData(obstData);
		const variation = hash % 6; // 创建6种不同的材质变化，增强对比度

		switch (variation) {
			case 0: // 标准建筑材质
				return { roughnessOffset: 0, metalnessOffset: 0, envMapOffset: 0 };
			case 1: // 粗糙混凝土表面
				return {
					roughnessOffset: 0.4,
					metalnessOffset: -0.2,
					envMapOffset: -0.3,
				};
			case 2: // 光滑金属表面
				return {
					roughnessOffset: -0.2,
					metalnessOffset: 0.4,
					envMapOffset: 0.4,
				};
			case 3: // 高反射金属
				return {
					roughnessOffset: -0.1,
					metalnessOffset: 0.5,
					envMapOffset: 0.5,
				};
			case 4: // 哑光塑料
				return {
					roughnessOffset: 0.3,
					metalnessOffset: -0.3,
					envMapOffset: -0.4,
				};
			case 5: // 半光泽表面
				return {
					roughnessOffset: 0.1,
					metalnessOffset: 0.2,
					envMapOffset: 0.2,
				};
			default:
				return { roughnessOffset: 0, metalnessOffset: 0, envMapOffset: 0 };
		}
	}

	// 简单的哈希函数，基于对象数据
	hashObstData(obstData) {
		if (!obstData || !obstData.XB) return 0;
		const xb = obstData.XB;
		return Math.abs(
			xb[0] * 31 +
				xb[1] * 37 +
				xb[2] * 41 +
				xb[3] * 43 +
				xb[4] * 47 +
				xb[5] * 53
		);
	}

	// 增强颜色对比度
	enhanceColorContrast(color) {
		const threeColor = new THREE.Color(color);

		// 获取HSL值
		const hsl = {};
		threeColor.getHSL(hsl);

		// 增强饱和度和亮度对比
		let { h, s, l } = hsl;

		// 增强饱和度
		s = Math.min(1.0, s * 1.3);

		// 调整亮度以增加对比度
		if (l < 0.5) {
			// 暗色调：稍微提亮
			l = Math.min(0.7, l * 1.4);
		} else {
			// 亮色调：保持或稍微调暗
			l = Math.max(0.3, l * 0.9);
		}

		// 应用调整后的HSL值
		threeColor.setHSL(h, s, l);

		return threeColor;
	}

	createMeshes(obsts, bounds, parser, surfData, group = null) {
		// 使用普通Mesh替代InstancedMesh
		if (obsts.length === 0) return;

		// 创建一个标准的盒子几何体
		const boxGeometry = new THREE.BoxGeometry(1, 1, 1);

		// 颜色映射
		const colorMap = parser.generateSurfaceColorMap(surfData);

		// 为每个障碍物创建单独的Mesh
		obsts.forEach((obst, index) => {
			// 确保每个obst都有_id
			if (!obst._id) {
				obst._id = `obst_${Date.now()}_${index}`;
			}

			const [x1, x2, y1, y2, z1, z2] = obst.XB.split(',').map(Number);

			const width = x2 - x1;
			const height = z2 - z1;
			const depth = y2 - y1;

			const posX = (x1 + x2) / 2 - bounds.centerX;
			const posY = (z1 + z2) / 2 - bounds.centerZ;
			const posZ = (y1 + y2) / 2 - bounds.centerY;

			// 创建FDS材质，传入对象数据以获得材质变化
			const material = this.createFdsMaterial(obst);

			// 设置颜色并增强对比度
			const obstColor = parser.getObstColor(obst, colorMap);
			// const enhancedColor = this.enhanceColorContrast(obstColor);
			material.color.set(obstColor);

			// 创建单独的Mesh
			const mesh = new THREE.Mesh(boxGeometry, material);

			// 设置位置和缩放
			mesh.position.set(posX, posY, posZ);
			mesh.scale.set(width, height, depth);

			// 设置阴影
			mesh.castShadow = true;
			mesh.receiveShadow = true;

			// 设置用户数据
			mesh.userData = {
				type: 'fds',
				obstData: obst,
				_id: obst._id,
				groupId: group ? group.id : null,
				groupLabel: group ? group.label : null,
			};

			// 如果有分组信息，添加到userData中
			if (group) {
				mesh.userData.groupId = group.id;
				mesh.userData.groupLabel = group.label;
			} else if (obst.groupId) {
				mesh.userData.groupId = obst.groupId;
				mesh.userData.groupLabel = obst.groupLabel;
			}

			// 添加到可点击对象列表
			this.clickableObjects.push(mesh);

			// 建立快速索引映射
			this.objectIndexMap.set(obst._id, mesh);

			// 添加到全局FDS对象集合
			this.allFdsObjects.add(mesh);

			// 建立分组映射
			let groupId = 'default'; // 设置默认值
			if (group && group.id) {
				groupId = group.id;
			} else if (obst.groupId && typeof obst.groupId === 'string') {
				groupId = obst.groupId;
			}

			// 确保groupId不是undefined
			if (groupId === undefined || groupId === null) {
				groupId = 'default';
			}

			if (!this.groupObjectsMap.has(groupId)) {
				this.groupObjectsMap.set(groupId, new Set());
			}
			this.groupObjectsMap.get(groupId).add(mesh);

			// 添加到场景
			this.scene.add(mesh);
		});
	}
	initWaterPlane() {
		// 使用WaterPlane组件创建水面
		const waterPlane = new WaterPlane(10000, 10000);
		this.water = waterPlane.createWaterPlane();
		this.scene.add(this.water);

		// 保存waterPlane实例以便后续更新
		this.waterPlane = waterPlane;

		// 添加一个大的地面平面来接收阴影
		this.initGroundPlane();
	}

	/**
	 * 初始化地面平面（用于接收阴影）
	 */
	initGroundPlane() {
		const groundGeometry = new THREE.PlaneGeometry(20000, 20000);
		const groundMaterial = new THREE.MeshStandardMaterial({
			color: 0x808080,
			transparent: true,
			opacity: 0.1, // 几乎透明，只用于接收阴影
		});

		this.groundPlane = new THREE.Mesh(groundGeometry, groundMaterial);
		this.groundPlane.rotation.x = -Math.PI / 2;
		this.groundPlane.position.y = -10; // 稍微低于水面
		this.groundPlane.receiveShadow = true;
		this.groundPlane.castShadow = false;

		this.scene.add(this.groundPlane);
	}

	// 添加状态更新方法
	updatePlaybackStatus() {
		if (this.playbackControls) {
			this.playbackControls.status = this.characterManager.isPlaying
				? '播放中'
				: '已暂停';

			// 只在非拖动状态下更新当前帧值，避免拖动时的冲突
			if (!this.isDragging) {
				this.playbackControls.currentFrame = this.characterManager.timeIndex;
			}

			// 获取时间信息
			const timeInfo = this.characterManager.getTimeInfo();

			// 在非拖动状态下，显示时间和帧数；在拖动状态下，只显示时间
			if (!this.isDragging) {
				this.playbackControls.currentTimeDisplay = `${timeInfo.currentTime}`;
			}

			this.playbackControls.totalTime = timeInfo.totalTime;

			// 更新进度条的最大值
			if (this.frameController && this.characterManager.moveData.length > 0) {
				this.frameController.max(this.characterManager.moveData.length - 1);
			}
		}
	}
	initStats() {
		this.stats = new Stats();
		this.stats.showPanel(0);
		document.body.appendChild(this.stats.dom);
	}
	// ==================== 光照系统方法 ====================

	/**
	 * 初始化场景光照
	 */
	initLight() {
		this._setupAmbientLighting();
		this._setupMainDirectionalLight();
		// this._setupAuxiliaryLights();
		this._setupCharacterLighting();
	}

	/**
	 * 设置环境光照
	 */
	_setupAmbientLighting() {
		// 适中的环境光强度 - 为人物提供基础照明但不掩盖平行光效果
		this.ambientLight = new THREE.AmbientLight(0xffffff, 0.3);
		this.scene.add(this.ambientLight);

		// // 增强半球光 - 提供更明亮的天空地面对比
		// this.hemisphereLight = new THREE.HemisphereLight(0x87ceeb, 0x8b7355, 1.0);
		// this.scene.add(this.hemisphereLight);
	}

	/**
	 * 设置环境光强度
	 * @param {number} intensity - 光强度值
	 */
	setAmbientLightIntensity(intensity) {
		if (this.ambientLight) {
			this.ambientLight.intensity = intensity;
			console.log('环境光强度已设置为:', intensity);
		}
	}

	/**
	 * 设置环境光颜色
	 * @param {string} color - 颜色值 (hex格式)
	 */
	setAmbientLightColor(color) {
		if (this.ambientLight) {
			this.ambientLight.color.setHex(color.replace('#', '0x'));
			console.log('环境光颜色已设置为:', color);
		}
	}

	/**
	 * 设置平行光强度
	 * @param {number} intensity - 光强度值
	 */
	setDirectionalLightIntensity(intensity) {
		if (this.mainDirectionalLight) {
			this.mainDirectionalLight.intensity = intensity;
			console.log('平行光强度已设置为:', intensity);
		}
	}

	/**
	 * 设置平行光颜色
	 * @param {string} color - 颜色值 (hex格式)
	 */
	setDirectionalLightColor(color) {
		if (this.mainDirectionalLight) {
			this.mainDirectionalLight.color.setHex(color.replace('#', '0x'));
			console.log('平行光颜色已设置为:', color);
		}
	}

	/**
	 * 设置平行光位置
	 * @param {number} x - X坐标
	 * @param {number} y - Y坐标
	 * @param {number} z - Z坐标
	 */
	setDirectionalLightPosition(x, y, z) {
		if (this.mainDirectionalLight) {
			this.mainDirectionalLight.position.set(x, y, z);
			console.log('平行光位置已设置为:', { x, y, z });
		}
	}

	/**
	 * 设置主要方向光（太阳光）
	 *
	 */
	_setupMainDirectionalLight() {
		this.mainDirectionalLight = new THREE.DirectionalLight(0xffffff, 2.0);
		this.mainDirectionalLight.position.set(-100, 160, -290);
		this.mainDirectionalLight.target.position.set(0, 0, 0);
		this.mainDirectionalLight.castShadow = true;

		// 配置阴影以扩大光源范围
		this._configureShadows(this.mainDirectionalLight);

		this.scene.add(this.mainDirectionalLight);
	}

	/**
	 * 配置光源阴影
	 * @param {THREE.DirectionalLight} light - 方向光
	 */
	_configureShadows(light) {
		light.shadow.mapSize.width = 4096;
		light.shadow.mapSize.height = 4096;
		light.shadow.camera.near = 0.1;
		light.shadow.camera.far = 2000;
		light.shadow.camera.left = -1500;
		light.shadow.camera.right = 1500;
		light.shadow.camera.top = 1500;
		light.shadow.camera.bottom = -1500;
	}

	/**
	 * 设置辅助光源
	 */
	_setupAuxiliaryLights() {
		const auxiliaryLights = [
			{
				target: [0, 0, 0],
				position: [500, 300, 300],
				intensity: 2.5,
				color: 0xffffff,
			},
			{
				target: [0, 0, 0],
				position: [-500, 300, 300],
				intensity: 2.5,
				color: 0xffffff,
			},
			{
				target: [0, 0, 0],
				position: [0, 300, 500],
				intensity: 2.0,
				color: 0xffffff,
			},
			{
				target: [0, 0, 0],
				position: [0, 300, -500],
				intensity: 2.0,
				color: 0xffffff,
			},
			{
				target: [0, 0, 0],
				position: [300, 200, 0],
				intensity: 1.5,
				color: 0xfff8dc, // 暖白色
			},
			{
				target: [0, 0, 0],
				position: [-300, 200, 0],
				intensity: 1.5,
				color: 0xfff8dc, // 暖白色
			},
		];

		auxiliaryLights.forEach((config, index) => {
			const light = new THREE.DirectionalLight(config.color, config.intensity);
			light.target.position.set(...config.target);
			light.position.set(...config.position);
			light.castShadow = false; // 辅助光不投射阴影，避免性能问题
			this.scene.add(light);
			this.scene.add(light.target);

			// 存储辅助光引用以便后续控制
			if (!this.auxiliaryLights) {
				this.auxiliaryLights = [];
			}
			this.auxiliaryLights.push(light);
		});
	}

	/**
	 * 设置人物专用光照
	 */
	_setupCharacterLighting() {
		// 主要人物补光 - 增强强度
		this.characterFillLight = new THREE.DirectionalLight(0xffffff, 0.5);
		this.characterFillLight.position.set(0, 150, 300);
		this.characterFillLight.target.position.set(0, 0, 0);
		this.characterFillLight.castShadow = false;
		this.scene.add(this.characterFillLight);
		this.scene.add(this.characterFillLight.target);

		// 侧面补光1 - 增强强度
		this.characterSideLight1 = new THREE.DirectionalLight(0xffffff, 0.5);
		this.characterSideLight1.position.set(-300, 100, 100);
		this.characterSideLight1.target.position.set(0, 0, 0);
		this.characterSideLight1.castShadow = false;
		this.scene.add(this.characterSideLight1);
		this.scene.add(this.characterSideLight1.target);

		// 侧面补光2 - 增强强度
		this.characterSideLight2 = new THREE.DirectionalLight(0xffffff, 0.5);
		this.characterSideLight2.position.set(300, 100, 100);
		this.characterSideLight2.target.position.set(0, 0, 0);
		this.characterSideLight2.castShadow = false;
		this.scene.add(this.characterSideLight2);
		this.scene.add(this.characterSideLight2.target);

		// 添加顶部补光
		this.characterTopLight = new THREE.DirectionalLight(0xffffff, 0.5);
		this.characterTopLight.position.set(0, 400, 0);
		this.characterTopLight.target.position.set(0, 0, 0);
		this.characterTopLight.castShadow = false;
		this.scene.add(this.characterTopLight);
		this.scene.add(this.characterTopLight.target);
	}

	// ==================== 相机和场景方法 ====================

	/**
	 * 初始化相机
	 */
	initCamera() {
		this.camera = new THREE.PerspectiveCamera(
			75,
			window.innerWidth / window.innerHeight,
			0.1,
			1000
		);

		this._setupInitialCameraPosition();
	}

	/**
	 * 设置相机初始位置
	 */
	_setupInitialCameraPosition() {
		// 设置正面45度俯视角度 - 从正前方斜向下看
		const initialPosition = new THREE.Vector3(0, 2, 3);
		const initialTarget = new THREE.Vector3(0, 0, 0);

		// 保存初始相机位置和方向，用于重置功能
		this.initialCameraPosition = initialPosition.clone();
		this.initialCameraTarget = initialTarget.clone();

		this.camera.position.copy(initialPosition);
		this.camera.lookAt(initialTarget);
	}

	/**
	 * 初始化天空盒
	 * @returns {Promise} 加载完成的Promise
	 */
	async initSkybox() {
		return new Promise((resolve) => {
			const loader = new THREE.CubeTextureLoader();
			const skyboxTextures = [
				'./box/left.png',
				'./box/right.png',
				'./box/up.png',
				'./box/down.png',
				'./box/front.png',
				'./box/back.png',
			];

			const texture = loader.load(skyboxTextures, () => {
				this.scene.background = texture;
				resolve();
			});
		});
	}
	initRenderer(container) {
		this.renderer = new THREE.WebGLRenderer({
			antialias: true,
		});

		// 使用传入的容器或默认容器
		const targetContainer =
			container ||
			document.querySelector('.main-content') ||
			document.getElementById('app');

		if (targetContainer) {
			// 查找主内容区容器
			const mainContentContainer = targetContainer.querySelector(
				'.scene-main-content'
			);
			const renderContainer = mainContentContainer || targetContainer;

			// 获取容器尺寸
			const rect = renderContainer.getBoundingClientRect();
			this.renderer.setSize(rect.width, rect.height);
			this.renderer.setPixelRatio(window.devicePixelRatio);
			this.renderer.setClearColor(0xffffff, 1);
			this.renderer.outputEncoding = THREE.sRGBEncoding;

			// 启用阴影和增强渲染效果
			this.renderer.shadowMap.enabled = true;
			this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

			// 优化色调映射以改善贴图颜色显示，提高曝光度增加亮度
			this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
			this.renderer.toneMappingExposure = 1.4; // 提高曝光度，增加整体亮度

			// 设置输出颜色空间
			this.renderer.outputColorSpace = THREE.SRGBColorSpace;

			// 将canvas添加到指定容器
			renderContainer.appendChild(this.renderer.domElement);

			// 保存容器引用用于resize
			this.container = renderContainer;
		} else {
		}
	}
	initControls() {
		this.controls = new PointerLockControls(this.camera, this.container);

		// this.renderer.domElement.addEventListener('click', () => {
		// 	this.controls.lock();
		// });

		document.addEventListener('keydown', (e) => this.onKeyDown(e));
		document.addEventListener('keyup', (e) => this.onKeyUp(e));

		// 添加鼠标事件监听
		this.renderer.domElement.addEventListener('click', (e) =>
			this.onMouseClick(e)
		);
		this.renderer.domElement.addEventListener('mousemove', (e) =>
			this.onMouseMove(e)
		);
	}
	onKeyDown(e) {
		// 更新按键状态
		const key = e.key.toLowerCase();
		if (key in this.keys) {
			this.keys[key] = true;
		}

		// 添加视角重置快捷键 (R键)
		if (key === 'r') {
			this.resetCamera();
		}
	}
	onKeyUp(e) {
		// 更新按键状态
		const key = e.key.toLowerCase();
		if (key in this.keys) {
			this.keys[key] = false;
		}
	}

	updateMovement() {
		// 获取相机的前进方向
		const forward = new THREE.Vector3();
		this.camera.getWorldDirection(forward);

		// 计算相机的右方向
		const right = new THREE.Vector3();
		right.crossVectors(forward, new THREE.Vector3(0, 1, 0)).normalize();

		// 计算移动方向
		const moveDirection = new THREE.Vector3(0, 0, 0);

		// 完全按照相机方向移动
		if (this.keys.w) moveDirection.add(forward);
		if (this.keys.s) moveDirection.sub(forward);
		if (this.keys.a) moveDirection.sub(right);
		if (this.keys.d) moveDirection.add(right);

		// 如果有移动输入，则标准化方向并应用移动
		if (moveDirection.length() > 0) {
			moveDirection.normalize();
			this.camera.position.addScaledVector(moveDirection, this.speed);
		}

		// 垂直移动
		if (this.keys.q) this.camera.position.y += this.speed;
		if (this.keys.e) this.camera.position.y -= this.speed;

		// 限制最低高度
		const minHeight = 0;
		if (this.camera.position.y < minHeight) {
			this.camera.position.y = minHeight;
		}
	}

	render() {
		this.renderer.render(this.scene, this.camera);
	}
	tick() {
		this.requestId = requestAnimationFrame(() => {
			const delta = this.clock.getDelta();
			if (!this.isDragging) {
				this.updateMovement(); // 添加移动更新
			}

			// 更新高亮框位置
			this.updateHighlight();

			// 更新水面动画 - 使用较小的系数减慢波纹速度
			if (this.waterPlane && this.water) {
				// 使用0.3作为系数减慢水波纹速度
				this.waterPlane.update(this.clock.getElapsedTime() * 0.5);
			}

			this.render();
			this.tick();
			this.characterManager.update(delta);
			// this.stats.update(); // 移除stats更新，现在使用Vue组件
		});
	}
	stop() {
		if (this.requestId) {
			cancelAnimationFrame(this.requestId);
		}

		// 清理高亮资源
		this.clearHighlight();

		// 移除事件监听器
		if (this.renderer && this.renderer.domElement) {
			this.renderer.domElement.removeEventListener('click', this.onMouseClick);
			this.renderer.domElement.removeEventListener(
				'mousemove',
				this.onMouseMove
			);
		}
	}

	onWindowResize() {
		if (this.container) {
			const rect = this.container.getBoundingClientRect();
			this.camera.aspect = rect.width / rect.height;
			this.camera.updateProjectionMatrix();
			this.renderer.setSize(rect.width, rect.height);
		} else {
			// 兜底处理
			this.camera.aspect = window.innerWidth / window.innerHeight;
			this.camera.updateProjectionMatrix();
			this.renderer.setSize(window.innerWidth, window.innerHeight);
		}
	}

	addResizeListener() {
		window.addEventListener('resize', () => this.onWindowResize());
	}

	// 鼠标点击事件处理
	onMouseClick(event) {
		event.preventDefault();

		// 如果正在拖动GUI，不处理点击事件
		if (this.isDragging) {
			return;
		}

		const clickedObject = this.getObjectAtPosition(event);

		if (clickedObject) {
			this.selectObject(clickedObject);
		} else {
			this.clearSelection();
		}
	}

	// 获取鼠标位置的对象
	getObjectAtPosition(event) {
		// 计算鼠标位置
		const rect = this.renderer.domElement.getBoundingClientRect();
		this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
		this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

		// 进行射线检测
		this.raycaster.setFromCamera(this.mouse, this.camera);

		// 过滤可点击对象，排除不可见的对象
		const visibleObjects = this.clickableObjects.filter((obj) => obj.visible);

		// 检测相交的对象
		const intersects = this.raycaster.intersectObjects(visibleObjects, true);

		if (intersects.length > 0) {
			const intersect = intersects[0];
			let clickedObject = intersect.object;

			// 如果点击的是子对象，向上查找可点击的父对象
			while (clickedObject && !this.clickableObjects.includes(clickedObject)) {
				clickedObject = clickedObject.parent;
			}

			if (clickedObject && this.clickableObjects.includes(clickedObject)) {
				return clickedObject;
			}
		}

		return null;
	}

	// 选择对象
	selectObject(object) {
		this.highlightObject(object);

		// 创建节点数据格式，用于通知外部
		const nodeData = this.createNodeDataFromObject(object);

		// 通知外部组件
		if (this.onObjectClick) {
			this.onObjectClick(nodeData);
		}
	}

	// 清除选择
	clearSelection() {
		this.clearHighlight();
	}

	// 鼠标移动事件处理（可选，用于悬停效果）
	onMouseMove() {
		// 暂时不实现悬停效果，避免性能问题
	}

	// 高亮对象
	highlightObject(object) {
		// 清除之前的高亮
		this.clearHighlight();

		// 设置当前高亮对象
		this.highlightedObject = object;

		if (object.userData.type === 'character') {
			// 人物模型：使用线框包围盒
			this.createWireframeHighlight(object);
		} else {
			// FDS模型：使用颜色高亮
			this.createColorHighlight(object);
		}
	}

	// 创建线框高亮（用于人物模型）
	createWireframeHighlight(object) {
		// 计算对象的包围盒
		const box = new THREE.Box3();

		// 确保模型矩阵是最新的
		object.updateMatrixWorld(true);

		// 计算整个模型的包围盒
		box.setFromObject(object);

		// 如果包围盒为空，尝试手动计算
		if (box.isEmpty()) {
			object.traverse((child) => {
				if (child.isMesh && child.geometry) {
					const childBox = new THREE.Box3().setFromObject(child);
					if (!childBox.isEmpty()) {
						box.union(childBox);
					}
				}
			});
		}

		// 检查包围盒是否有效
		if (box.isEmpty()) {
			return;
		}

		// 创建包围盒的线框几何体
		const size = box.getSize(new THREE.Vector3());
		const center = box.getCenter(new THREE.Vector3());

		// 考虑场景的全局缩放（场景缩放为0.1）
		const sceneScale = this.scene.scale.x;
		const adjustedSize = size.clone().divideScalar(sceneScale);
		const adjustedCenter = center.clone().divideScalar(sceneScale);

		// 确保尺寸有效
		if (adjustedSize.x <= 0 || adjustedSize.y <= 0 || adjustedSize.z <= 0) {
			return;
		}

		// 创建科技感高亮效果组
		this.highlightGroup = new THREE.Group();

		// 1. 创建透明背景包围盒
		const boxGeometry = new THREE.BoxGeometry(
			adjustedSize.x,
			adjustedSize.y,
			adjustedSize.z
		);

		// 创建透明背景材质 - 使用醒目的红色
		const backgroundMaterial = new THREE.MeshBasicMaterial({
			color: 0xff0000, // 纯红色，非常醒目
			transparent: true,
			opacity: 0.2,
			depthTest: false,
			depthWrite: false,

			side: THREE.DoubleSide,
		});

		this.highlightBackground = new THREE.Mesh(boxGeometry, backgroundMaterial);
		this.highlightBackground.scale.multiplyScalar(1.05);

		// 2. 创建扫描线效果
		this.createScanLines(adjustedSize);

		// 将所有效果添加到组中
		this.highlightGroup.add(this.highlightBackground);
		if (this.scanLines) this.highlightGroup.add(this.scanLines);

		// 设置组的位置
		this.highlightGroup.position.copy(adjustedCenter);

		// 保存尺寸信息供动画使用
		this.highlightGroup.userData = { size: adjustedSize };

		// 添加到场景
		this.scene.add(this.highlightGroup);

		// 启动动画
		this.startHighlightAnimation();

		// 同时改变人物模型的颜色
		this.highlightCharacterColor(object);
	}

	// 创建扫描线效果
	createScanLines(size) {
		const scanLineGeometry = new THREE.PlaneGeometry(size.x * 1.2, 0.02);
		const scanLineMaterial = new THREE.MeshBasicMaterial({
			color: 0xff3333, // 明亮的红色扫描线
			transparent: true,
			opacity: 0.7,
			depthTest: false,
			depthWrite: false,
		});

		this.scanLines = new THREE.Mesh(scanLineGeometry, scanLineMaterial);
		this.scanLines.position.y = -size.y / 2;
	}

	// 启动高亮动画
	startHighlightAnimation() {
		if (this.highlightAnimationId) {
			cancelAnimationFrame(this.highlightAnimationId);
		}

		const animate = () => {
			const time = Date.now() * 0.003;

			// 透明背景脉冲效果
			if (this.highlightBackground) {
				this.highlightBackground.material.opacity =
					0.1 + Math.sin(time * 2) * 0.05;
				// 轻微的缩放动画
				const scale = 1.05 + Math.sin(time * 1.5) * 0.02;
				this.highlightBackground.scale.setScalar(scale);
			}

			// 扫描线移动
			if (this.scanLines) {
				const size = this.highlightGroup.userData?.size || { y: 2 };
				this.scanLines.position.y =
					-size.y / 2 + (Math.sin(time) * 0.5 + 0.5) * size.y;
				this.scanLines.material.opacity = 0.4 + Math.sin(time * 4) * 0.2;
			}

			// 角落标记闪烁
			if (this.cornerMarkers) {
				this.cornerMarkers.children.forEach((marker, index) => {
					marker.material.opacity =
						0.6 + Math.sin(time * 2 + index * 0.5) * 0.2;
					marker.scale.setScalar(1 + Math.sin(time * 3 + index * 0.3) * 0.2);
				});
			}

			this.highlightAnimationId = requestAnimationFrame(animate);
		};

		animate();
	}

	// 停止高亮动画
	stopHighlightAnimation() {
		if (this.highlightAnimationId) {
			cancelAnimationFrame(this.highlightAnimationId);
			this.highlightAnimationId = null;
		}
	}

	// 高亮人物模型颜色
	highlightCharacterColor(object) {
		// 保存原始材质
		this.originalCharacterMaterials = new Map();

		// 遍历所有子网格
		object.traverse((child) => {
			if (child.isMesh && child.material) {
				// 保存原始材质
				this.originalCharacterMaterials.set(child.id, child.material);

				// 创建高亮材质
				const highlightMaterial = child.material.clone();
				highlightMaterial.emissive = new THREE.Color(0x00bfff); // 明亮的蓝色发光效果
				highlightMaterial.emissiveIntensity = 0.3; // 适中的发光强度

				// 添加轻微的透明效果
				highlightMaterial.transparent = true;
				highlightMaterial.opacity = 0.95;

				// 应用高亮材质
				child.material = highlightMaterial;
			}
		});
	}

	// 创建颜色高亮（用于FDS模型）
	createColorHighlight(object) {
		// 保存原始材质
		this.originalMaterial = object.material;

		// 创建科技感高亮材质
		const highlightMaterial = object.material.clone();

		// 设置主要颜色为明亮的蓝色
		highlightMaterial.color.setHex(0x00bfff);

		// 添加明亮的发光效果
		highlightMaterial.emissive.setHex(0x00bfff);
		highlightMaterial.emissiveIntensity = 0.4;

		// 增加金属感和反射
		highlightMaterial.metalness = 0.6;
		highlightMaterial.roughness = 0.3;

		// 添加轻微透明度
		highlightMaterial.transparent = true;
		highlightMaterial.opacity = 0.9;

		// 应用高亮材质
		object.material = highlightMaterial;

		// 创建边缘发光效果（静态，无动画）
		this.createFdsEdgeGlow(object);
	}

	// 创建FDS模型边缘发光效果
	createFdsEdgeGlow(object) {
		// 创建边缘发光几何体
		const edgeGeometry = new THREE.EdgesGeometry(object.geometry);
		const edgeMaterial = new THREE.LineBasicMaterial({
			color: 0xffff00, // 明亮的纯黄色边缘线
			transparent: true,
			opacity: 0.8,
			linewidth: 1,
			depthTest: false,
			depthWrite: false,
		});

		this.fdsEdgeGlow = new THREE.LineSegments(edgeGeometry, edgeMaterial);

		// 复制对象的变换，但不放大，保持原始尺寸
		this.fdsEdgeGlow.position.copy(object.position);
		this.fdsEdgeGlow.rotation.copy(object.rotation);
		this.fdsEdgeGlow.scale.copy(object.scale);
		// 移除放大效果，让线框更贴合

		// 添加到场景
		this.scene.add(this.fdsEdgeGlow);
	}

	// 创建节点数据（与StructureTree组件格式一致）
	createNodeDataFromObject(object) {
		if (object.userData.type === 'character') {
			const personData = object.userData.personData;
			// 从人员数据中获取索引，如果没有则使用默认值
			const personIndex = personData.index !== undefined ? personData.index : 0;
			return {
				id: personData._id,
				label: `${personData.name || `人员${personIndex + 1}`} (${
					personData.age
				}岁)`,
				type: 'person',
				data: personData,
				gender: personData.sex,
			};
		} else if (object.userData.type === 'fds') {
			const obstData = object.userData.obstData;
			return {
				id: obstData._id,
				label: obstData.ID,
				type: 'fds_object',
				data: obstData,
				groupId: object.userData.groupId,
				groupLabel: object.userData.groupLabel,
			};
		}
		return null;
	}

	// 移除highlightInstance方法，因为我们不再使用InstancedMesh

	// 更新高亮框位置
	updateHighlight() {
		if (this.highlightedObject) {
			if (
				this.highlightedObject.userData &&
				this.highlightedObject.userData.type === 'character'
			) {
				// 人物模型的高亮更新逻辑
				if (this.highlightGroup) {
					// 重新计算包围盒
					const box = new THREE.Box3();
					this.highlightedObject.updateMatrixWorld(true);
					box.setFromObject(this.highlightedObject);

					if (!box.isEmpty()) {
						const center = box.getCenter(new THREE.Vector3());
						// 考虑场景的全局缩放
						const sceneScale = this.scene.scale.x;
						const adjustedCenter = center.clone().divideScalar(sceneScale);

						// 更新高亮组位置
						this.highlightGroup.position.copy(adjustedCenter);
					}
				}

				// 向后兼容：处理旧的线框高亮
				if (this.highlightWireframe && !this.highlightGroup) {
					const box = new THREE.Box3();
					this.highlightedObject.updateMatrixWorld(true);
					box.setFromObject(this.highlightedObject);

					if (!box.isEmpty()) {
						const center = box.getCenter(new THREE.Vector3());
						const sceneScale = this.scene.scale.x;
						const adjustedCenter = center.clone().divideScalar(sceneScale);
						this.highlightWireframe.position.copy(adjustedCenter);
					}
				}
			} else if (this.highlightedObject.userData?.type === 'fds') {
				// FDS模型的高亮更新逻辑（静态，无需动画更新）
				if (this.fdsEdgeGlow) {
					// 同步边缘发光的位置和变换
					this.fdsEdgeGlow.position.copy(this.highlightedObject.position);
					this.fdsEdgeGlow.rotation.copy(this.highlightedObject.rotation);
					this.fdsEdgeGlow.scale.copy(this.highlightedObject.scale);
					// 保持原始尺寸，不放大
				}
			}
			// InstancedMesh高亮不需要更新位置，因为实例不会移动
		}
	}

	// 清除高亮
	clearHighlight() {
		// 停止人物模型高亮动画
		this.stopHighlightAnimation();

		// 清除人物模型的高亮组
		if (this.highlightGroup) {
			this.scene.remove(this.highlightGroup);

			// 释放组内所有资源
			this.highlightGroup.traverse((child) => {
				if (child.geometry) child.geometry.dispose();
				if (child.material) child.material.dispose();
			});

			this.highlightGroup = null;
		}

		// 清除透明背景
		if (this.highlightBackground) {
			if (this.highlightBackground.geometry) {
				this.highlightBackground.geometry.dispose();
			}
			if (this.highlightBackground.material) {
				this.highlightBackground.material.dispose();
			}
			this.highlightBackground = null;
		}

		// 清除旧的线框高亮（向后兼容）
		if (this.highlightWireframe) {
			this.scene.remove(this.highlightWireframe);
			if (this.highlightWireframe.geometry) {
				this.highlightWireframe.geometry.dispose();
			}
			if (this.highlightWireframe.material) {
				this.highlightWireframe.material.dispose();
			}
			this.highlightWireframe = null;
		}

		// 清除内层线框
		if (this.innerHighlightWireframe) {
			if (this.innerHighlightWireframe.geometry) {
				this.innerHighlightWireframe.geometry.dispose();
			}
			if (this.innerHighlightWireframe.material) {
				this.innerHighlightWireframe.material.dispose();
			}
			this.innerHighlightWireframe = null;
		}

		// 清除扫描线
		if (this.scanLines) {
			if (this.scanLines.geometry) this.scanLines.geometry.dispose();
			if (this.scanLines.material) this.scanLines.material.dispose();
			this.scanLines = null;
		}

		// 清除FDS边缘发光
		if (this.fdsEdgeGlow) {
			this.scene.remove(this.fdsEdgeGlow);
			if (this.fdsEdgeGlow.geometry) this.fdsEdgeGlow.geometry.dispose();
			if (this.fdsEdgeGlow.material) this.fdsEdgeGlow.material.dispose();
			this.fdsEdgeGlow = null;
		}

		// 清除颜色高亮（普通FDS模型）
		if (this.highlightedObject && this.originalMaterial) {
			// 恢复原始材质
			this.highlightedObject.material = this.originalMaterial;
			this.originalMaterial = null;
		}

		// 清除人物模型的颜色高亮
		if (this.highlightedObject && this.originalCharacterMaterials) {
			// 恢复所有原始材质
			this.highlightedObject.traverse((child) => {
				if (child.isMesh && this.originalCharacterMaterials.has(child.id)) {
					child.material = this.originalCharacterMaterials.get(child.id);
				}
			});
			this.originalCharacterMaterials.clear();
			this.originalCharacterMaterials = null;
		}

		this.highlightedObject = null;
	}

	// 设置对象点击回调
	setObjectClickCallback(callback) {
		this.onObjectClick = callback;
	}

	// 根据节点数据高亮对象
	highlightObjectByNodeData(nodeData) {
		// 清除之前的高亮
		this.clearHighlight();

		const object = this.findObjectByNodeData(nodeData);
		if (object && object.visible) {
			this.highlightObject(object);
		}
	}

	// 根据节点数据查找对象（高性能版本）
	findObjectByNodeData(nodeData) {
		if (!nodeData) return null;

		if (nodeData.type === 'person') {
			// 查找人物模型
			const characterIndex = nodeData.personIndex;
			if (
				this.characterManager &&
				this.characterManager.characters[characterIndex]
			) {
				return this.characterManager.characters[characterIndex];
			}
		} else if (nodeData.type === 'fds_object') {
			// 使用高性能索引查找FDS对象
			if (nodeData.id) {
				const mesh = this.objectIndexMap.get(nodeData.id);
				if (mesh) {
					return mesh;
				}
			}

			// 如果高性能查找失败，回退到传统查找
			return this.findFdsObjectByData(nodeData);
		}

		return null;
	}

	// 查找FDS对象的统一方法
	findFdsObjectByData(nodeData) {
		if (!nodeData || !nodeData.data) return null;

		const data = nodeData.data;

		// 优先通过_id查找
		if (data._id) {
			const object = this.clickableObjects.find(
				(obj) => obj.userData.type === 'fds' && obj.userData._id === data._id
			);
			if (object) return object;
		}

		// 通过ID和分组查找
		if (data.ID) {
			const object = this.clickableObjects.find(
				(obj) =>
					obj.userData.type === 'fds' &&
					obj.userData.obstData &&
					obj.userData.obstData.ID === data.ID &&
					(!nodeData.groupId ||
						!obj.userData.groupId ||
						obj.userData.groupId === nodeData.groupId)
			);
			if (object) return object;
		}

		// 通过索引查找
		if (nodeData.fdsIndex !== undefined) {
			const object = this.clickableObjects.find(
				(obj) =>
					obj.userData.type === 'fds' &&
					obj.userData.index === nodeData.fdsIndex
			);
			if (object) return object;
		}

		return null;
	}

	// 重置相机到初始位置和方向
	resetCamera() {
		// 重置相机位置
		this.camera.position.copy(this.initialCameraPosition);

		// 重置相机方向
		this.camera.lookAt(this.initialCameraTarget);

		// 如果需要，也重置控制器的状态
		if (this.controls && this.controls.unlock) {
			this.controls.unlock();
		}
	}

	// 设置对象可见性
	setObjectVisibility(object, visible) {
		if (!object) return false;

		// 直接设置对象的可见性
		object.visible = visible;

		// 如果对象被高亮，并且要隐藏它，清除高亮
		if (!visible && this.highlightedObject === object) {
			this.clearHighlight();
		}

		return true;
	}

	// 获取对象可见性
	getObjectVisibility(object) {
		if (!object) return true;
		return object.visible;
	}

	// ===== 高性能隐藏/显示方法 =====

	// 通过ID隐藏单个对象
	hideObjectById(objectId) {
		const mesh = this.objectIndexMap.get(objectId);
		if (mesh && mesh.visible) {
			mesh.visible = false;
			this.hiddenObjects.add(mesh);

			// 如果对象被高亮，清除高亮
			if (this.highlightedObject === mesh) {
				this.clearHighlight();
			}
			return true;
		}

		return false;
	}

	// 通过ID显示单个对象
	showObjectById(objectId) {
		const mesh = this.objectIndexMap.get(objectId);
		if (mesh && !mesh.visible) {
			mesh.visible = true;
			this.hiddenObjects.delete(mesh);
			return true;
		}
		return false;
	}

	// 批量隐藏组内对象
	hideObjectsByGroupId(groupId) {
		const objects = this.groupObjectsMap.get(groupId);
		if (!objects) {
			console.log('未找到组:', groupId);
			return [];
		}

		const hiddenIds = [];
		objects.forEach((mesh) => {
			if (mesh.visible) {
				mesh.visible = false;
				this.hiddenObjects.add(mesh);
				hiddenIds.push(mesh.userData._id);

				// 如果对象被高亮，清除高亮
				if (this.highlightedObject === mesh) {
					this.clearHighlight();
				}
			}
		});
		console.log('成功隐藏对象数量:', hiddenIds.length);
		return hiddenIds;
	}

	// 批量显示组内对象
	showObjectsByGroupId(groupId) {
		const objects = this.groupObjectsMap.get(groupId);
		if (!objects) return [];

		const shownIds = [];
		objects.forEach((mesh) => {
			if (!mesh.visible && this.hiddenObjects.has(mesh)) {
				mesh.visible = true;
				this.hiddenObjects.delete(mesh);
				shownIds.push(mesh.userData._id);
			}
		});
		return shownIds;
	}

	// 获取组内隐藏对象的数量
	getHiddenCountInGroup(groupId) {
		const objects = this.groupObjectsMap.get(groupId);
		if (!objects) {
			console.log('组不存在:', groupId, '返回默认状态');
			// 组不存在时，应该返回一个明确表示"未找到"的状态
			// 而不是让系统误判为"全部隐藏"
			return { hidden: 0, total: -1 }; // total为-1表示组不存在
		}

		let hiddenCount = 0;
		objects.forEach((mesh) => {
			if (this.hiddenObjects.has(mesh)) {
				hiddenCount++;
			}
		});

		const result = {
			hidden: hiddenCount,
			total: objects.size,
		};

		console.log('组状态结果:', result);
		return result;
	}

	// 检查对象是否隐藏
	isObjectHidden(objectId) {
		const mesh = this.objectIndexMap.get(objectId);
		return mesh ? this.hiddenObjects.has(mesh) : false;
	}

	// 显示所有隐藏的对象
	showAllObjects() {
		const shownIds = [];
		this.hiddenObjects.forEach((mesh) => {
			mesh.visible = true;
			shownIds.push(mesh.userData._id);
		});
		this.hiddenObjects.clear();
		return shownIds;
	}

	// 获取所有隐藏对象的ID列表
	getHiddenObjectIds() {
		const hiddenIds = [];
		this.hiddenObjects.forEach((mesh) => {
			hiddenIds.push(mesh.userData._id);
		});
		return hiddenIds;
	}

	// 隐藏所有FDS对象
	hideAllFdsObjects() {
		const hiddenIds = [];
		this.allFdsObjects.forEach((mesh) => {
			if (mesh.visible) {
				mesh.visible = false;
				this.hiddenObjects.add(mesh);
				hiddenIds.push(mesh.userData._id);

				// 如果对象被高亮，清除高亮
				if (this.highlightedObject === mesh) {
					this.clearHighlight();
				}
			}
		});
		return hiddenIds;
	}

	// 显示所有FDS对象
	showAllFdsObjects() {
		const shownIds = [];
		this.allFdsObjects.forEach((mesh) => {
			if (!mesh.visible && this.hiddenObjects.has(mesh)) {
				mesh.visible = true;
				this.hiddenObjects.delete(mesh);
				shownIds.push(mesh.userData._id);
			}
		});
		return shownIds;
	}

	// 获取所有FDS对象的隐藏统计
	getAllFdsHiddenCount() {
		let hiddenCount = 0;
		this.allFdsObjects.forEach((mesh) => {
			if (this.hiddenObjects.has(mesh)) {
				hiddenCount++;
			}
		});
		return {
			hidden: hiddenCount,
			total: this.allFdsObjects.size,
		};
	}

	// 统一更新FDS材质透明度
	setFdsTransparency(transparency) {
		this.currentTransparency = transparency;
		this.fdsMaterials.forEach((material) => {
			const materialName = material.userData?.name || '';
			if (['墙', '窗'].some((item) => materialName.includes(item))) {
				material.opacity = transparency;
				material.transparent = true;
				material.needsUpdate = true;
			}
		});
	}

	// 获取FDS材质配置
	getFdsMaterialConfig() {
		return { ...this.materialConfig.fds };
	}

	// 更新FDS材质配置
	updateFdsMaterialConfig(config) {
		Object.assign(this.materialConfig.fds, config);
		// 应用到所有现有材质
		this.fdsMaterials.forEach((material) => {
			Object.keys(config).forEach((key) => {
				if (material[key] !== undefined) {
					material[key] = config[key];
				}
			});
		});
	}

	// 不再需要自定义射线检测方法，使用Three.js原生的raycaster.intersectObjects

	// 设置环境光强度
	setAmbientLightIntensity(intensity) {
		if (this.ambientLight) {
			this.ambientLight.intensity = intensity;
		}
	}

	// 设置环境光颜色
	setAmbientLightColor(color) {
		if (this.ambientLight) {
			this.ambientLight.color.setHex(color);
		}
	}

	// 设置主平行光强度
	setDirectionalLightIntensity(intensity) {
		if (this.mainDirectionalLight) {
			this.mainDirectionalLight.intensity = intensity;
		}
	}

	// 设置主平行光颜色
	setDirectionalLightColor(color) {
		if (this.mainDirectionalLight) {
			this.mainDirectionalLight.color.setHex(color);
		}
	}

	// 设置半球光强度
	setHemisphereLightIntensity(intensity) {
		if (this.hemisphereLight) {
			this.hemisphereLight.intensity = intensity;
		}
	}

	// 设置渲染器曝光度
	setToneMappingExposure(exposure) {
		if (this.renderer) {
			this.renderer.toneMappingExposure = exposure;
		}
	}

	// 批量调整场景亮度 - 提供预设的亮度级别
	setSceneBrightness(level) {
		// level: 'dark' | 'normal' | 'bright' | 'very-bright'
		const presets = {
			'very-dark': {
				ambientLight: 0.3,
				hemisphereLight: 0.3,
				directionalLight: 1.0,
				exposure: 0.8,
			},
			dark: {
				ambientLight: 0.5,
				hemisphereLight: 0.4,
				directionalLight: 1.5,
				exposure: 1.0,
			},
			normal: {
				ambientLight: 1.2,
				hemisphereLight: 0.8,
				directionalLight: 0.8,
				exposure: 1.4,
			},
			bright: {
				ambientLight: 1.5,
				hemisphereLight: 1.0,
				directionalLight: 3.0,
				exposure: 1.6,
			},
			'very-bright': {
				ambientLight: 2.0,
				hemisphereLight: 1.3,
				directionalLight: 3.5,
				exposure: 2.0,
			},
		};

		const preset = presets[level];
		if (preset) {
			this.setAmbientLightIntensity(preset.ambientLight);
			this.setHemisphereLightIntensity(preset.hemisphereLight);
			this.setDirectionalLightIntensity(preset.directionalLight);
			this.setToneMappingExposure(preset.exposure);
		}
	}

	// 设置水面颜色
	setWaterColor(color) {
		if (this.waterPlane) {
			this.waterPlane.setWaterColor(color);
		}
	}

	// 设置水面透明度
	setWaterOpacity(opacity) {
		if (this.waterPlane) {
			this.waterPlane.setWaterOpacity(opacity);
		}
	}

	// 设置水面为舒适的颜色
	setWaterComfortableColor() {
		if (this.waterPlane) {
			this.waterPlane.setComfortableColor();
		}
	}

	// 设置水面为深蓝色
	setWaterDeepBlue() {
		if (this.waterPlane) {
			this.waterPlane.setDeepBlue();
		}
	}

	// 设置水面为浅蓝色
	setWaterLightBlue() {
		if (this.waterPlane) {
			this.waterPlane.setLightBlue();
		}
	}

	// 设置人物材质颜色
	setCharacterColor(color) {
		if (this.characterManager) {
			this.characterManager.setCharacterColor(color);
		}
	}

	// 设置人物材质透明度
	setCharacterOpacity(opacity) {
		if (this.characterManager) {
			this.characterManager.setCharacterOpacity(opacity);
		}
	}

	// 重置人物材质
	resetCharacterMaterial() {
		if (this.characterManager) {
			this.characterManager.resetCharacterMaterial();
		}
	}

	// 应用自然的人物材质
	applyNaturalCharacterMaterial() {
		if (this.characterManager) {
			this.characterManager.applyNaturalCharacterMaterial();
		}
	}

	// 优化人物亮度
	optimizeCharacterBrightness() {
		if (this.characterManager) {
			this.characterManager.optimizeCharacterBrightness();
		}
	}
}
