<template>
	<div class="scene-control-panel">
		<!-- 视角控制 -->
		<div class="panel-section">
			<div class="section-header">
				<div class="header-content">
					<el-icon class="header-icon"><View /></el-icon>
					<span class="header-title">视角控制</span>
				</div>
			</div>
			<div class="section-content">
				<div class="action-item">
					<div class="item-content">
						<el-icon class="item-icon"><RefreshRight /></el-icon>
						<span class="item-label">重置视角</span>
						<span class="item-shortcut">R</span>
					</div>
					<div class="item-actions">
						<el-button
							size="small"
							text
							@click="handleCameraReset"
							class="action-btn"
							title="重置视角"
						>
							<el-icon><RefreshRight /></el-icon>
						</el-button>
					</div>
				</div>

				<!-- 相机速度控制 -->
				<div class="slider-container">
					<div class="slider-header">
						<span class="slider-label">相机移动速度</span>
						<span class="slider-value"
							>{{ Math.round(cameraSpeed * 100) }}%</span
						>
					</div>
					<el-slider
						v-model="cameraSpeed"
						:min="0.01"
						:max="0.1"
						:step="0.01"
						:show-tooltip="false"
						@change="handleCameraSpeedChange"
					/>
				</div>
			</div>
		</div>

		<!-- 场景控制 -->
		<div class="panel-section">
			<div class="section-header">
				<div class="header-content">
					<el-icon class="header-icon"><Monitor /></el-icon>
					<span class="header-title">场景控制</span>
				</div>
			</div>
			<div class="section-content">
				<div class="action-item">
					<div class="item-content">
						<el-icon class="item-icon"><View /></el-icon>
						<span class="item-label">显示所有对象</span>
					</div>
					<div class="item-actions">
						<el-button
							size="small"
							text
							@click="handleResetScene"
							class="action-btn show-btn"
							title="显示所有对象"
						>
							<el-icon><View /></el-icon>
						</el-button>
					</div>
				</div>

				<!-- 透明度控制 -->
				<div class="slider-container">
					<div class="slider-header">
						<span class="slider-label">建筑透明度</span>
						<span class="slider-value"
							>{{ Math.round(transparencyValue * 100) }}%</span
						>
					</div>
					<el-slider
						v-model="transparencyValue"
						:min="0"
						:max="1"
						:step="0.05"
						:show-tooltip="false"
						@change="handleTransparencyChange"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, watch } from 'vue';
	import { RefreshRight, View, Monitor } from '@element-plus/icons-vue';

	const props = defineProps({
		characterManager: {
			type: Object,
			default: null,
		},
		transparency: {
			type: Number,
			default: 0.3,
		},
		viewEngine: {
			type: Object,
			default: null,
		},
	});

	const emit = defineEmits([
		'transparency-change',
		'camera-reset',
		'reset-scene',
		'camera-speed-change',
	]);

	// 控制面板状态
	const transparencyValue = ref(props.transparency);
	const cameraSpeed = ref(props.viewEngine?.speed || 0.03);

	// 处理透明度变化
	const handleTransparencyChange = (value) => {
		emit('transparency-change', value);
	};

	// 处理相机速度变化
	const handleCameraSpeedChange = (value) => {
		emit('camera-speed-change', value);
		if (props.viewEngine) {
			props.viewEngine.speed = value;
		}
	};

	// 处理相机重置
	const handleCameraReset = () => {
		emit('camera-reset');
	};

	// 处理场景重置
	const handleResetScene = () => {
		emit('reset-scene');
	};

	// 监听props变化
	watch(
		() => props.transparency,
		(newValue) => {
			transparencyValue.value = newValue;
		}
	);

	watch(
		() => props.viewEngine?.speed,
		(newValue) => {
			if (newValue !== undefined) {
				cameraSpeed.value = newValue;
			}
		}
	);
</script>

<style scoped>
	.scene-control-panel {
		height: 100%;
		overflow-y: auto;
		background: var(--app-surface-1);
	}

	/* 面板部分样式 */
	.panel-section {
		margin-bottom: var(--app-space-md);
	}

	.section-header {
		padding: var(--app-space-sm) var(--app-space-md);
		background: var(--app-surface-2);
		border-bottom: 1px solid var(--app-border-primary);
	}

	.header-content {
		display: flex;
		align-items: center;
		gap: var(--app-space-sm);
	}

	.header-icon {
		font-size: 16px;
		color: var(--app-primary);
	}

	.header-title {
		font-size: var(--app-font-size-base);
		font-weight: var(--app-font-weight-medium);
		color: var(--app-text-primary);
	}

	.section-content {
		padding: var(--app-space-sm) var(--app-space-md);
	}

	/* 操作项样式 */
	.action-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: var(--app-space-xs) 0;
		border-bottom: 1px solid var(--app-border-secondary);
	}

	.item-content {
		display: flex;
		align-items: center;
		gap: var(--app-space-sm);
		flex: 1;
	}

	.item-icon {
		font-size: 16px;
		color: var(--app-text-secondary);
	}

	.item-label {
		font-size: var(--app-font-size-sm);
		color: var(--app-text-primary);
	}

	.item-shortcut {
		font-size: var(--app-font-size-xs);
		color: var(--app-text-tertiary);
		background: var(--app-surface-3);
		padding: 2px 6px;
		border-radius: var(--app-radius-small);
		margin-left: var(--app-space-sm);
	}

	.item-actions {
		display: flex;
		align-items: center;
	}

	/* 按钮样式 */
	.action-btn {
		padding: var(--app-space-xs);
		border-radius: 4px;
		margin-left: 2px;
		color: var(--app-primary);
		display: flex;
		align-items: center;
		gap: 4px;
	}

	.show-btn {
		color: var(--app-success);
	}

	.show-btn:hover {
		color: var(--app-success);
		background-color: rgba(82, 196, 26, 0.1);
	}

	/* 滑块容器 */
	.slider-container {
		padding: var(--app-space-sm) 0;
	}

	.slider-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: var(--app-space-sm);
	}

	.slider-label {
		font-size: var(--app-font-size-sm);
		color: var(--app-text-secondary);
	}

	.slider-value {
		font-size: var(--app-font-size-xs);
		background: var(--app-primary);
		color: white;
		padding: 2px 6px;
		border-radius: var(--app-radius-small);
	}
</style>
