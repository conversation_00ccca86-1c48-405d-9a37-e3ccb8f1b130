<template>
	<div
		class="person-detail-panel"
		v-show="visible && personData"
		:class="{ 'panel-visible': visible }"
	>
		<div class="panel-header">
			<div class="panel-title">
				<el-icon
					class="title-icon"
					:class="genderIconClass"
				>
					<component :is="genderIcon" />
				</el-icon>
				<span>人员信息</span>
			</div>
			<el-button
				size="small"
				text
				@click="$emit('close')"
				class="close-btn"
			>
				<el-icon><Close /></el-icon>
			</el-button>
		</div>

		<div class="panel-content">
			<!-- 头像区域 -->
			<div class="person-avatar">
				<div
					class="avatar-circle"
					:class="genderClass"
				>
					<el-icon class="avatar-icon">
						<component :is="genderIcon" />
					</el-icon>
				</div>
			</div>

			<!-- 信息区域 -->
			<div class="person-info">
				<div class="info-item id-item">
					<div class="info-label">ID</div>
					<div class="info-value id-value">
						{{
							personData?.id ||
							personData?.name ||
							`人员${(personData?.index || 0) + 1}`
						}}
					</div>
				</div>

				<div class="info-row">
					<div class="info-item">
						<div class="info-label">性别</div>
						<div
							class="info-value gender-value"
							:class="genderClass"
						>
							<el-icon class="gender-icon">
								<component :is="genderIcon" />
							</el-icon>
							{{ genderText }}
						</div>
					</div>
					<div class="info-item">
						<div class="info-label">年龄</div>
						<div class="info-value age-value">{{ personData?.age }}岁</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { computed } from 'vue';
	import { User, Male, Female, Close } from '@element-plus/icons-vue';

	const props = defineProps({
		visible: {
			type: Boolean,
			default: false,
		},
		personData: {
			type: Object,
			default: null,
		},
	});

	const emit = defineEmits(['close']);

	// 性别相关计算属性
	const genderText = computed(() => {
		return props.personData?.sex === 'M' ? '男性' : '女性';
	});

	const genderIcon = computed(() => {
		return props.personData?.sex === 'M' ? Male : Female;
	});

	const genderIconClass = computed(() => {
		return props.personData?.sex === 'M' ? 'icon-male' : 'icon-female';
	});

	const genderClass = computed(() => {
		return props.personData?.sex === 'M' ? 'male' : 'female';
	});

	// 年龄组计算属性
	const ageGroupText = computed(() => {
		if (!props.personData?.age) return '未知';
		const age = props.personData.age;
		if (age < 18) return '儿童';
		if (age >= 60) return '老年人';
		return '成年人';
	});

	// 格式化坐标
	const formatCoordinate = (value) => {
		if (typeof value === 'number') {
			return value.toFixed(2);
		}
		return value || '未知';
	};

	// 其他属性
	const otherProperties = computed(() => {
		if (!props.personData) return {};

		const excludeKeys = ['name', 'age', 'sex', 'index', 'position', '_id'];
		const result = {};

		Object.keys(props.personData).forEach((key) => {
			if (
				!excludeKeys.includes(key) &&
				props.personData[key] !== undefined &&
				props.personData[key] !== null
			) {
				result[key] = props.personData[key];
			}
		});

		return result;
	});

	const hasOtherProperties = computed(() => {
		return Object.keys(otherProperties.value).length > 0;
	});

	// 格式化属性名
	const formatPropertyName = (key) => {
		const nameMap = {
			id: 'ID',
			type: '类型',
			status: '状态',
			group: '分组',
			floor: '楼层',
			room: '房间',
		};
		return nameMap[key] || key;
	};

	// 格式化属性值
	const formatPropertyValue = (value) => {
		if (typeof value === 'object') {
			return JSON.stringify(value);
		}
		return String(value);
	};
</script>

<style scoped>
	.person-detail-panel {
		position: absolute;
		top: 20px;
		left: 20px;
		width: 280px;
		background: rgba(255, 255, 255, 0.98);
		backdrop-filter: blur(15px);
		border-radius: 16px;
		box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
		border: 1px solid rgba(255, 255, 255, 0.3);
		z-index: 1000;
		transform: translateX(-100%);
		opacity: 0;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		overflow: hidden;
	}

	.panel-visible {
		transform: translateX(0);
		opacity: 1;
	}

	.panel-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16px 20px;
		border-bottom: 1px solid rgba(0, 0, 0, 0.06);
		background: linear-gradient(135deg, #f8fafc, #e2e8f0);
	}

	.panel-title {
		display: flex;
		align-items: center;
		gap: 8px;
		font-weight: 600;
		color: #1f2937;
	}

	.title-icon.icon-male {
		color: #3b82f6;
	}

	.title-icon.icon-female {
		color: #ec4899;
	}

	.close-btn {
		color: #6b7280;
		padding: 4px;
	}

	.close-btn:hover {
		color: #ef4444;
		background-color: rgba(239, 68, 68, 0.1);
	}

	.panel-content {
		padding: 24px;
	}

	/* 头像区域 */
	.person-avatar {
		display: flex;
		justify-content: center;
		margin-bottom: 20px;
	}

	.avatar-circle {
		width: 60px;
		height: 60px;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		transition: transform 0.2s ease;
	}

	.avatar-circle:hover {
		transform: scale(1.05);
	}

	.avatar-circle.male {
		background: linear-gradient(135deg, #3b82f6, #1d4ed8);
	}

	.avatar-circle.female {
		background: linear-gradient(135deg, #ec4899, #be185d);
	}

	.avatar-icon {
		font-size: 28px;
		color: white;
	}

	/* 信息区域 */
	.person-info {
		display: flex;
		flex-direction: column;
		gap: 16px;
	}

	.info-item {
		display: flex;
		flex-direction: column;
		gap: 4px;
	}

	.id-item {
		text-align: center;
		padding: 12px;
		background: linear-gradient(135deg, #f8fafc, #e2e8f0);
		border-radius: 12px;
		border: 2px solid #e5e7eb;
	}

	.info-row {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 12px;
	}

	.info-label {
		font-size: 12px;
		color: #6b7280;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.info-value {
		font-size: 16px;
		font-weight: 700;
		color: #1f2937;
	}

	.id-value {
		font-size: 18px;
		color: #3b82f6;
		text-align: center;
	}

	.gender-value {
		display: flex;
		align-items: center;
		gap: 6px;
		padding: 8px 12px;
		border-radius: 8px;
		font-size: 14px;
		font-weight: 600;
	}

	.gender-value.male {
		background: rgba(59, 130, 246, 0.1);
		color: #1d4ed8;
	}

	.gender-value.female {
		background: rgba(236, 72, 153, 0.1);
		color: #be185d;
	}

	.gender-icon {
		font-size: 16px;
	}

	.age-value {
		padding: 8px 12px;
		background: rgba(16, 185, 129, 0.1);
		color: #047857;
		border-radius: 8px;
		text-align: center;
		font-weight: 600;
	}

	.info-section {
		margin-bottom: 20px;
	}

	.info-section:last-child {
		margin-bottom: 0;
	}

	.section-title {
		font-size: 14px;
		font-weight: 600;
		color: #374151;
		margin-bottom: 12px;
		padding-bottom: 6px;
		border-bottom: 2px solid #e5e7eb;
	}

	.info-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 12px;
	}

	.info-item {
		background: #f9fafb;
		padding: 12px;
		border-radius: 8px;
		border-left: 3px solid #3b82f6;
	}

	.info-label {
		font-size: 12px;
		color: #6b7280;
		margin-bottom: 4px;
		font-weight: 500;
	}

	.info-value {
		font-size: 14px;
		color: #1f2937;
		font-weight: 600;
	}

	/* 滚动条样式 */
	.person-detail-panel::-webkit-scrollbar {
		width: 4px;
	}

	.person-detail-panel::-webkit-scrollbar-track {
		background: transparent;
	}

	.person-detail-panel::-webkit-scrollbar-thumb {
		background: rgba(0, 0, 0, 0.2);
		border-radius: 2px;
	}

	.person-detail-panel::-webkit-scrollbar-thumb:hover {
		background: rgba(0, 0, 0, 0.3);
	}
</style>
