<template>
	<Transition
		name="loading-fade"
		appear
	>
		<div
			v-if="props.visible"
			class="loading-overlay"
		>
			<!-- 背景 -->
			<div class="loading-background">
				<div class="bg-gradient"></div>
			</div>

			<!-- 主内容 -->
			<div class="loading-content">
				<!-- 顶部加载器和标题 -->
				<div class="loading-header">
					<!-- 主加载器 -->
					<div class="main-loader">
						<div class="loader-rings">
							<div class="ring ring-1"></div>
							<div class="ring ring-2"></div>
							<div class="ring ring-3"></div>
						</div>
						<div class="loader-center">
							<el-icon :size="32">
								<Operation />
							</el-icon>
						</div>
					</div>

					<!-- 标题和描述 -->
					<div class="loading-text">
						<h2 class="loading-title">{{ title }}</h2>
						<p class="loading-subtitle">{{ currentStepName }}</p>
					</div>

					<!-- 总进度 -->
					<div class="total-progress">
						<div class="progress-bar">
							<div
								class="progress-fill"
								:style="{ width: totalProgress + '%' }"
							></div>
						</div>
						<span class="progress-text">{{ Math.round(totalProgress) }}%</span>
					</div>
				</div>

				<!-- 步骤网格 -->
				<div
					v-if="steps.length > 0"
					class="steps-container"
				>
					<div class="steps-header">
						<h3 class="steps-title">加载进度</h3>
						<span class="steps-count"
							>{{ currentStepIndex + 1 }} / {{ steps.length }}</span
						>
					</div>

					<div class="steps-list">
						<div
							v-for="(step, index) in steps"
							:key="index"
							class="step-item"
							:class="getStepClass(step.status, index)"
						>
							<div class="step-icon-wrapper">
								<div class="step-icon">
									<el-icon v-if="step.status === 'completed'">
										<Check />
									</el-icon>
									<el-icon
										v-else-if="step.status === 'active'"
										class="rotating"
									>
										<Loading />
									</el-icon>
									<span
										v-else
										class="step-number"
										>{{ index + 1 }}</span
									>
								</div>
								<div
									v-if="step.status === 'active'"
									class="step-pulse"
								></div>
							</div>
							<div class="step-content">
								<div class="step-name">{{ step.name }}</div>
								<div
									v-if="step.status === 'active'"
									class="step-progress"
								>
									<div
										class="step-progress-bar"
										:style="{ width: stepProgress + '%' }"
									></div>
								</div>
							</div>
							<div
								class="step-connector"
								v-if="index < steps.length - 1"
							></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</Transition>
</template>

<script setup>
	import { computed } from 'vue';
	import {
		Loading,
		Check,
		Operation,
		InfoFilled,
	} from '@element-plus/icons-vue';

	// Props
	const props = defineProps({
		visible: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '正在加载资源',
		},
		steps: {
			type: Array,
			default: () => [],
		},
		currentStepIndex: {
			type: Number,
			default: 0,
		},
		stepProgress: {
			type: Number,
			default: 0,
		},
		statusText: {
			type: String,
			default: '初始化中...',
		},
	});

	// 计算总进度
	const totalProgress = computed(() => {
		if (props.steps.length === 0) return 0;

		const totalWeight = props.steps.reduce(
			(sum, step) => sum + (step.weight || 1),
			0
		);
		let completedWeight = 0;

		// 计算已完成步骤的权重
		for (let i = 0; i < props.currentStepIndex; i++) {
			if (props.steps[i].status === 'completed') {
				completedWeight += props.steps[i].weight || 1;
			}
		}

		// 加上当前步骤的部分权重
		const currentStep = props.steps[props.currentStepIndex];
		if (currentStep && currentStep.status === 'active') {
			const currentStepWeight = currentStep.weight || 1;
			completedWeight += (currentStepWeight * props.stepProgress) / 100;
		}

		return Math.min(100, (completedWeight / totalWeight) * 100);
	});

	// 当前步骤名称
	const currentStepName = computed(() => {
		const currentStep = props.steps[props.currentStepIndex];
		return currentStep ? currentStep.name : '准备中...';
	});

	// 获取步骤样式类
	const getStepClass = (status, index) => {
		const classes = [`step-${status}`];
		if (index === props.currentStepIndex) {
			classes.push('step-current');
		}
		return classes;
	};
</script>

<style scoped>
	/* 全屏遮罩 */
	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		z-index: 9999;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
	}

	/* 背景设计 */
	.loading-background {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}

	.bg-gradient {
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
	}

	/* 主内容容器 */
	.loading-content {
		position: relative;
		z-index: 10;
		max-width: 350px;
		width: 90%;
		display: flex;
		flex-direction: column;
		gap: 0;
	}

	/* 顶部区域 */
	.loading-header {
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20px);
		border: 1px solid rgba(74, 144, 226, 0.15);
		border-radius: 16px 16px 0 0;
		padding: 24px 20px 20px;
		text-align: center;
		box-shadow: 0 8px 32px rgba(74, 144, 226, 0.08);
	}

	/* 主加载器 */
	.main-loader {
		position: relative;
		width: 60px;
		height: 60px;
		margin: 0 auto 16px;
	}

	.loader-rings {
		position: absolute;
		width: 100%;
		height: 100%;
	}

	.ring {
		position: absolute;
		border: 3px solid transparent;
		border-radius: 50%;
		animation: ring-spin linear infinite;
	}

	.ring-1 {
		width: 60px;
		height: 60px;
		border-top-color: #409eff;
		animation-duration: 3s;
	}

	.ring-2 {
		width: 45px;
		height: 45px;
		top: 7.5px;
		left: 7.5px;
		border-right-color: #337ecc;
		animation-duration: 2s;
		animation-direction: reverse;
	}

	.ring-3 {
		width: 30px;
		height: 30px;
		top: 15px;
		left: 15px;
		border-bottom-color: #66b1ff;
		animation-duration: 1.5s;
	}

	.loader-center {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: #409eff;
		animation: center-pulse 2s ease-in-out infinite;
	}

	@keyframes ring-spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	@keyframes center-pulse {
		0%,
		100% {
			transform: translate(-50%, -50%) scale(1);
		}
		50% {
			transform: translate(-50%, -50%) scale(1.1);
		}
	}

	/* 文字区域 */
	.loading-text {
		margin-bottom: 16px;
	}

	.loading-title {
		font-size: 20px;
		font-weight: 700;
		color: #212529;
		margin: 0 0 4px 0;
		letter-spacing: 0.5px;
	}

	.loading-subtitle {
		font-size: 13px;
		color: #6c757d;
		margin: 0;
		font-weight: 400;
	}

	/* 总进度条 */
	.total-progress {
		display: flex;
		align-items: center;
		gap: 12px;
	}

	.progress-bar {
		flex: 1;
		height: 6px;
		background: #e9ecef;
		border-radius: 3px;
		overflow: hidden;
	}

	.progress-fill {
		height: 100%;
		background: linear-gradient(90deg, #409eff 0%, #337ecc 50%, #409eff 100%);
		border-radius: 4px;
		transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		overflow: hidden;
	}

	.progress-fill::after {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(
			90deg,
			transparent,
			rgba(255, 255, 255, 0.4),
			transparent
		);
		animation: progress-shimmer 2s infinite;
	}

	@keyframes progress-shimmer {
		0% {
			left: -100%;
		}
		100% {
			left: 100%;
		}
	}

	.progress-text {
		font-size: 16px;
		font-weight: 600;
		color: #212529;
		min-width: 45px;
		text-align: right;
	}

	/* 步骤容器 */
	.steps-container {
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20px);
		border: 1px solid rgba(74, 144, 226, 0.15);
		border-top: none;
		border-radius: 0 0 16px 16px;
		padding: 16px 20px 20px;
		box-shadow: 0 4px 16px rgba(74, 144, 226, 0.05);
	}

	.steps-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12px;
	}

	.steps-title {
		font-size: 16px;
		font-weight: 600;
		color: #212529;
		margin: 0;
	}

	.steps-count {
		font-size: 14px;
		color: #6c757d;
		background: #f8f9fa;
		padding: 4px 12px;
		border-radius: 12px;
		border: 1px solid #e9ecef;
		font-weight: 500;
	}

	/* 步骤列表 */
	.steps-list {
		display: flex;
		flex-direction: column;
		gap: 0;
	}

	.step-item {
		display: flex;
		align-items: flex-start;
		padding: 8px 12px;
		border-radius: 8px;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		border: 1px solid transparent;
		position: relative;
		margin-bottom: 4px;
	}

	/* 步骤状态样式 */
	.step-pending {
		background: #f8f9fa;
		border-color: #e9ecef;
		color: #adb5bd;
	}

	.step-active {
		background: rgba(64, 158, 255, 0.1);
		border-color: #409eff;
		color: #409eff;
		box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
		transform: translateY(-2px);
	}

	.step-completed {
		background: rgba(103, 194, 58, 0.1);
		border-color: #67c23a;
		color: #67c23a;
	}

	/* 步骤图标 */
	.step-icon-wrapper {
		position: relative;
		margin-right: 10px;
		flex-shrink: 0;
	}

	.step-icon {
		width: 24px;
		height: 24px;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #f8f9fa;
		border: 2px solid #e9ecef;
		transition: all 0.3s ease;
		color: #6c757d;
		font-size: 10px;
		font-weight: 600;
	}

	.step-active .step-icon {
		background: #409eff;
		border-color: #409eff;
		color: white;
		box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
		animation: step-pulse 2s infinite;
	}

	.step-completed .step-icon {
		background: #67c23a;
		border-color: #67c23a;
		color: white;
	}

	.step-pulse {
		position: absolute;
		top: -2px;
		left: -2px;
		width: 28px;
		height: 28px;
		border: 2px solid rgba(64, 158, 255, 0.3);
		border-radius: 50%;
		animation: pulse-ring 2s infinite;
	}

	@keyframes pulse-ring {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		100% {
			transform: scale(1.3);
			opacity: 0;
		}
	}

	@keyframes step-pulse {
		0%,
		100% {
			transform: scale(1);
			box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
		}
		50% {
			transform: scale(1.05);
			box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
		}
	}

	.rotating {
		animation: icon-rotate 1s linear infinite;
	}

	@keyframes icon-rotate {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	/* 步骤内容 */
	.step-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.step-name {
		font-size: 13px;
		font-weight: 500;
		color: inherit;
		line-height: 1.2;
		margin-bottom: 4px;
	}

	.step-active .step-name {
		font-weight: 600;
	}

	.step-progress {
		width: 100%;
		height: 3px;
		background: rgba(64, 158, 255, 0.2);
		border-radius: 2px;
		overflow: hidden;
	}

	.step-progress-bar {
		height: 100%;
		background: linear-gradient(90deg, #409eff, #66b1ff);
		border-radius: 2px;
		transition: width 0.3s ease;
	}

	/* 底部区域 */
	.loading-footer {
		text-align: center;
		margin-top: 12px;
		padding-top: 12px;
		border-top: 1px solid rgba(74, 144, 226, 0.1);
	}

	.tip-content {
		display: inline-flex;
		align-items: center;
		gap: 6px;
		color: #6c757d;
		font-size: 12px;
		font-weight: 400;
	}

	.tip-icon {
		font-size: 14px;
		opacity: 0.7;
	}

	/* 进入/退出动画 */
	.loading-fade-enter-active {
		transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
	}

	.loading-fade-leave-active {
		transition: all 0.4s cubic-bezier(0.7, 0, 0.84, 0);
	}

	.loading-fade-enter-from {
		opacity: 0;
		transform: scale(0.9) translateY(20px);
	}

	.loading-fade-leave-to {
		opacity: 0;
		transform: scale(1.1) translateY(-20px);
	}

	/* 响应式设计 */
	@media (max-width: 768px) {
		.loading-content {
			max-width: 380px;
		}

		.loading-header {
			padding: 28px 20px 20px;
		}

		.loading-title {
			font-size: 22px;
		}

		.main-loader {
			width: 70px;
			height: 70px;
		}

		.ring-1 {
			width: 70px;
			height: 70px;
		}

		.ring-2 {
			width: 52px;
			height: 52px;
			top: 9px;
			left: 9px;
		}

		.ring-3 {
			width: 35px;
			height: 35px;
			top: 17.5px;
			left: 17.5px;
		}

		.steps-container {
			padding: 16px 20px 20px;
		}

		.step-item {
			padding: 10px;
		}

		.step-icon {
			width: 26px;
			height: 26px;
			font-size: 10px;
		}

		.step-pulse {
			width: 30px;
			height: 30px;
		}

		.step-name {
			font-size: 13px;
		}
	}

	@media (max-width: 480px) {
		.loading-content {
			width: 95%;
			max-width: 320px;
		}

		.loading-header {
			padding: 24px 16px 16px;
		}

		.loading-title {
			font-size: 20px;
		}

		.main-loader {
			width: 60px;
			height: 60px;
		}

		.tip-content {
			font-size: 11px;
		}
	}
</style>
