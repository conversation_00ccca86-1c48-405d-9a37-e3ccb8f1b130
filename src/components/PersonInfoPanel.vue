<template>
	<div
		class="person-info-panel"
		v-show="visible"
		:class="{ 'panel-visible': visible }"
	>
		<div class="panel-header">
			<div class="panel-title">
				<el-icon class="title-icon"><User /></el-icon>
				<span>人员信息统计</span>
			</div>
			<el-button
				size="small"
				text
				@click="$emit('close')"
				class="close-btn"
			>
				<el-icon><Close /></el-icon>
			</el-button>
		</div>

		<div class="panel-content">
			<div class="stats-overview">
				<div class="stat-item total">
					<div class="stat-number">{{ totalCount }}</div>
					<div class="stat-label">总人数</div>
				</div>
			</div>

			<div class="stats-breakdown">
				<div class="breakdown-title">年龄分布</div>
				<div class="breakdown-list">
					<div
						class="breakdown-item"
						v-if="elderCount > 0"
					>
						<div class="item-icon elder">
							<el-icon><User /></el-icon>
						</div>
						<div class="item-content">
							<div class="item-label">老年人</div>
							<div class="item-desc">60岁及以上</div>
						</div>
						<div class="item-count">{{ elderCount }}</div>
					</div>

					<div
						class="breakdown-item"
						v-if="adultCount > 0"
					>
						<div class="item-icon adult">
							<el-icon><User /></el-icon>
						</div>
						<div class="item-content">
							<div class="item-label">成年人</div>
							<div class="item-desc">18-59岁</div>
						</div>
						<div class="item-count">{{ adultCount }}</div>
					</div>

					<div
						class="breakdown-item"
						v-if="childCount > 0"
					>
						<div class="item-icon child">
							<el-icon><User /></el-icon>
						</div>
						<div class="item-content">
							<div class="item-label">儿童</div>
							<div class="item-desc">18岁以下</div>
						</div>
						<div class="item-count">{{ childCount }}</div>
					</div>
				</div>
			</div>

			<div class="stats-breakdown">
				<div class="breakdown-title">性别分布</div>
				<div class="breakdown-list">
					<div
						class="breakdown-item"
						v-if="maleCount > 0"
					>
						<div class="item-icon male">
							<el-icon><Male /></el-icon>
						</div>
						<div class="item-content">
							<div class="item-label">男性</div>
						</div>
						<div class="item-count">{{ maleCount }}</div>
					</div>

					<div
						class="breakdown-item"
						v-if="femaleCount > 0"
					>
						<div class="item-icon female">
							<el-icon><Female /></el-icon>
						</div>
						<div class="item-content">
							<div class="item-label">女性</div>
						</div>
						<div class="item-count">{{ femaleCount }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { computed, watch } from 'vue';
	import { User, Close, Male, Female } from '@element-plus/icons-vue';

	const props = defineProps({
		visible: {
			type: Boolean,
			default: false,
		},
		personData: {
			type: Array,
			default: () => [],
		},
	});

	const emit = defineEmits(['close']);

	// 计算总人数
	const totalCount = computed(() => props.personData.length);

	// 计算年龄分布
	const elderCount = computed(
		() => props.personData.filter((person) => person.age >= 60).length
	);

	const adultCount = computed(
		() =>
			props.personData.filter((person) => person.age >= 18 && person.age < 60)
				.length
	);

	const childCount = computed(
		() => props.personData.filter((person) => person.age < 18).length
	);

	// 计算性别分布
	const maleCount = computed(
		() => props.personData.filter((person) => person.sex === 'M').length
	);

	const femaleCount = computed(
		() => props.personData.filter((person) => person.sex === 'F').length
	);

	// 调试信息
	watch(
		() => props.visible,
		(newVal) => {
			console.log('PersonInfoPanel visible changed:', newVal);
			console.log('PersonInfoPanel personData:', props.personData);
			console.log('PersonInfoPanel totalCount:', totalCount.value);
		}
	);

	watch(
		() => props.personData,
		(newVal) => {
			console.log('PersonInfoPanel personData changed:', newVal);
		},
		{ deep: true }
	);
</script>

<style scoped>
	.person-info-panel {
		position: fixed;
		top: 20px;
		left: 20px;
		width: 280px;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		border-radius: 12px;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
		border: 1px solid rgba(255, 255, 255, 0.2);
		z-index: 1000;
		transform: translateX(-100%);
		opacity: 0;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.panel-visible {
		transform: translateX(0);
		opacity: 1;
	}

	.panel-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 16px 20px;
		border-bottom: 1px solid rgba(0, 0, 0, 0.06);
	}

	.panel-title {
		display: flex;
		align-items: center;
		gap: 8px;
		font-weight: 600;
		color: #1f2937;
	}

	.title-icon {
		color: #3b82f6;
	}

	.close-btn {
		color: #6b7280;
		padding: 4px;
	}

	.close-btn:hover {
		color: #ef4444;
		background-color: rgba(239, 68, 68, 0.1);
	}

	.panel-content {
		padding: 20px;
	}

	.stats-overview {
		margin-bottom: 24px;
	}

	.stat-item.total {
		text-align: center;
		padding: 16px;
		background: linear-gradient(135deg, #3b82f6, #1d4ed8);
		border-radius: 8px;
		color: white;
	}

	.stat-number {
		font-size: 32px;
		font-weight: 700;
		line-height: 1;
	}

	.stat-label {
		font-size: 14px;
		opacity: 0.9;
		margin-top: 4px;
	}

	.stats-breakdown {
		margin-bottom: 20px;
	}

	.breakdown-title {
		font-size: 14px;
		font-weight: 600;
		color: #374151;
		margin-bottom: 12px;
	}

	.breakdown-list {
		display: flex;
		flex-direction: column;
		gap: 8px;
	}

	.breakdown-item {
		display: flex;
		align-items: center;
		gap: 12px;
		padding: 8px 12px;
		background: #f9fafb;
		border-radius: 6px;
		transition: background-color 0.2s;
	}

	.breakdown-item:hover {
		background: #f3f4f6;
	}

	.item-icon {
		width: 32px;
		height: 32px;
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: white;
	}

	.item-icon.elder {
		background: #8b5cf6;
	}

	.item-icon.adult {
		background: #10b981;
	}

	.item-icon.child {
		background: #f59e0b;
	}

	.item-icon.male {
		background: #3b82f6;
	}

	.item-icon.female {
		background: #ec4899;
	}

	.item-content {
		flex: 1;
	}

	.item-label {
		font-size: 14px;
		font-weight: 500;
		color: #1f2937;
	}

	.item-desc {
		font-size: 12px;
		color: #6b7280;
	}

	.item-count {
		font-size: 16px;
		font-weight: 600;
		color: #1f2937;
		min-width: 24px;
		text-align: right;
	}
</style>
